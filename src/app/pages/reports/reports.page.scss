ion-toolbar {
  padding-inline: 10px;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.0125em;

}

.refreshButton {
  --border-color: #868686 !important;
  --border-width: 1px !important;
  --border-radius: 8px !important;
  font-weight: 600 !important;
}

.date-control {
  --border-color: var(--ion-color-step-300, #b3b3b3);
  --border-radius: 4px;
  --border-width: 1px;
  --min-height: 43px;
}

p {
  margin: 0 !important;
}

.orange {
  --progress-background: orange;
  color: orange;
}

.light-Green {
  --progress-background: rgb(105, 226, 105);
  color: rgb(105, 226, 105);
}

.darak-Green {
  --progress-background: rgb(13, 172, 13);
  color: rgb(13, 172, 13);
}

.light-Grey {
  --progress-background: indianred;
  color: indianred;
}


.orange-border {
  border-left: 3px solid orange;
}

.light-Green-border {
  border-left: 3px solid rgb(105, 226, 105);
}

.darak-Green-border {
  border-left: 3px solid rgb(13, 172, 13);
}

.light-Grey-border {
  border-left: 3px solid indianred;
}

.overdue {
  color: orangered;
}

.cancelled {
  color: brown;
}

.extended {
  color: #60b2be;
}

.open {
  color: rgb(13, 172, 13);
}

ion-row {
  justify-content: center;
}

ion-card {
  cursor: pointer;
  // border: 1px solid grey;
  border-radius: 10px;
  box-shadow: none;
  border: 1px solid #dee3ea;
}

p {
  margin-top: 10%;
  font-weight: 600;
}

ion-grid {
  height: 100%;
  padding: 0;
  margin: 0;
}

ion-content {
  --padding-start: 0px;
  --padding-end: 0px;
  --padding-top: 0px;
  --padding-bottom: 0px;
  --margin-top: 0px;
}

/* Add a small space at the top of the content area */
ion-content::part(scroll) {
  padding-top: 10px;
}

.mainBlock {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  grid-gap: 10px;
  padding: 0;
  margin: 0;
  margin-top: 0;
  margin-bottom: 10px;
}

.grid-item {
  text-align: center;
}

/* Odometer styles */
.odometer {
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

.odometer.odometer-theme-minimal .odometer-digit {
  color: inherit;
}

.odometer.odometer-theme-minimal .odometer-digit .odometer-digit-inner {
  text-align: center;
}

.odometer-inside {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Timeline chart styles */
::ng-deep .ngx-charts {
  .x-axis {
    .tick {
      text {
        font-size: 12px !important;
        font-weight: normal !important;
      }
    }
  }

  .line-chart .line-series .line {
    stroke-width: 3px; /* Make lines thicker */
  }

  .line-chart .circle {
    r: 5; /* Make data points larger */
  }
}

.headerFont {
  font-weight: 600;
}

/* Shimmer loading effect */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.shimmer {
  background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
  background-size: 800px 104px;
  animation: shimmer 1.5s infinite linear;
  border-radius: 8px;
}

.shimmer-container {
  width: 100%;
  padding: 0;
}

.shimmer-card {
  height: 100px;
  margin-bottom: 10px;
  border-radius: 8px;
  @extend .shimmer;
}

.shimmer-chart {
  height: 250px;
  margin: 10px 0;
  border-radius: 8px;
  @extend .shimmer;
}

/* Permits table styles */
.permits-content {
  padding: 16px;
  height: 100vh;
  overflow-y: auto;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: #f6f7f8;
  z-index: 10;
}

.back-to-charts {
  margin-bottom: 16px;

  ion-button {
    --color: #3b82f6;
    font-weight: 500;
  }
}

.permits-table-wrapper {
  overflow-x: auto;
  padding: 0;
}

.permits-table {
  width: 100%;
  margin-bottom: 20px;
  background-color: transparent;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  color: #475569;
  height: 40px;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
}

.table-row {
  display: flex;
  background-color: white;
  border-radius: 0;
  margin-bottom: 1px;
  height: 60px;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.2s ease-in-out;
  cursor: pointer;
  position: relative;

  &:hover {
    background-color: #f8fafc;
  }

  &:last-child {
    border-radius: 0 0 8px 8px;
  }
}

.header-cell, .table-cell {
  padding: 8px 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-cell {
  width: 120px;
  flex: 0 0 120px;
}

.permit-no-cell {
  width: 150px;
  flex: 0 0 150px;
}

.desc-cell {
  flex: 1;
  min-width: 200px;
}

.type-cell {
  width: 120px;
  flex: 0 0 120px;
}

.date-cell {
  width: 180px;
  flex: 0 0 180px;
}

.requester-cell {
  width: 280px;
  flex: 0 0 280px;
}

.actions-cell {
  width: 90px;
  flex: 0 0 90px;
  text-align: center;

  ion-button {
    --padding-start: 8px;
    --padding-end: 8px;
    margin: 0;
    height: 36px;
  }

  ion-icon {
    font-size: 18px;
  }
}

.status-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
}

.outline-badge {
  background-color: transparent;
  border: 1px solid currentColor;
}

.extended-badge, .expired-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  background-color: #3b82f6;
  color: white;
}

.expired-badge {
  background-color: #ef4444;
}

.date-with-icon {
  display: flex;
  align-items: center;
  gap: 4px;
}

.extension-icon {
  color: #3b82f6;
  font-size: 14px;
}

.expired-date {
  color: #ef4444;
}

// Zero Data State styling
.zero-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  background-color: #f9fafb;
}

.zero-data-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.zero-data-icon {
  margin-bottom: 24px;

  ion-icon {
    font-size: 80px;
    color: #94a3b8;
    opacity: 0.7;
  }
}

.zero-data-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
  margin-top: 0;
}

.zero-data-message {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 32px;
  margin-top: 0;
}

.zero-data-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;

  ion-button {
    --border-radius: 8px;
    --padding-start: 20px;
    --padding-end: 20px;
    height: 44px;
    font-weight: 500;

    &[fill="solid"] {
      --background: var(--ion-color-primary);
      --color: white;
    }

    &[fill="outline"] {
      --border-color: #d1d5db;
      --color: #6b7280;
    }
  }
}

// Empty state styling for permits list
.no-results-message {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #64748b;

  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #94a3b8;
  }

  p {
    font-size: 16px;
    margin-bottom: 16px;
  }
}

// Responsive styles
@media (max-width: 1200px) {
  .permits-table {
    margin: 0 0 20px;
  }

  .permits-table-wrapper {
    overflow-x: auto;
  }

  .table-header, .table-row {
    min-width: 1300px;
  }
}

@media (max-width: 768px) {
  .zero-data-container {
    min-height: 50vh;
    padding: 20px 16px;
  }

  .zero-data-content {
    padding: 30px 20px;
  }

  .zero-data-icon ion-icon {
    font-size: 60px;
  }

  .zero-data-title {
    font-size: 20px;
  }

  .zero-data-message {
    font-size: 14px;
  }

  .zero-data-actions {
    flex-direction: column;

    ion-button {
      width: 100%;
      margin: 4px 0;
    }
  }
}

@media (max-width: 768px) {
  .table-header {
    display: none;
  }

  .table-row {
    flex-direction: column;
    height: auto;
    min-width: auto;
    align-items: flex-start;
    padding: 15px;
  }

  .table-cell {
    width: 100% !important;
    min-width: auto !important;
    flex: none !important;
    padding: 5px 0;
    white-space: normal;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-cell:before {
    content: attr(data-label);
    font-weight: 600;
    width: 40%;
    color: #64748b;
  }
}