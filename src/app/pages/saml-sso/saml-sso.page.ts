import { Compo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LoadingController, Platform } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { AuthenticateAndActivateResultType, LoginListenerType, LoginParameters, LoginResult, LoginType, SettingsResult, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from 'src/app/shared/app-constants';
import { DataService } from 'src/app/services/data.service';
import { USER_HEADER } from 'src/app/data-models/data_classes';
@Component({
  selector: 'app-saml-sso',
  templateUrl: './saml-sso.page.html',
  styleUrls: ['./saml-sso.page.scss'],
})
export class SamlSsoPage implements OnInit, OnDestroy {
  token: string;
  action: string;
  public constants: AppConstants;
  language: string;
  private subscriptions = new Subscription()

  constructor(private router: ActivatedRoute,
    public unviredSDK: UnviredCordovaSDK,
    public ngZone: NgZone,
    private route: Router,
    public dataService: DataService,
    public loadingController: LoadingController, 
    private platform: Platform,
    public translate: TranslateService) { }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe()
  }

  ngOnInit() {

    this.constants = new AppConstants();

    if (localStorage.getItem('locale')) {
      this.language = localStorage.getItem('locale');
    } else {
      this.language = "English";
    }

    let subs = this.router.queryParams.subscribe(async params => {

      this.action = params['action'];
      this.token = params['token'];

      console.log('Action: ' + this.action + ' Token: ' + this.token)

      await this.initializeLoginProcess()

    });
    this.subscriptions.add(subs)
  }

  async initializeLoginProcess() {
    try {

      console.log('Waiting for Platform to be ready.')
      await this.platform.ready();
      console.log('Platform is ready')

      let loginParameters: LoginParameters = new LoginParameters();
      loginParameters.appName = this.constants.LOGIN_PARAMS.APP_NAME;
      loginParameters.metadataPath = this.constants.LOGIN_PARAMS.METADATA_PATH;
      loginParameters.autoSyncTime = '10'
      loginParameters.cacheWebData = true;
      loginParameters['samlToken'] = this.token
      loginParameters.url = this.dataService.getUmpUrl()
      loginParameters['appVersion'] = this.constants.APP_RELEASE_NUMBER;

      let loginResult: LoginResult = null;
      loginResult = await this.unviredSDK.login(loginParameters);
      console.log("login result", loginResult.type);

      switch (loginResult.type) {
        case LoginListenerType.auth_activation_required:

          // If token is present, set the token and call Authenticate and Activate.
          // Otherwise, call login screen.
          if (this.token) {
            await this.carmeuseLogin(loginParameters)
          }
          else {
            // ERROR SCENARIO.
            // SAML SSO screen and there is no token
            console.log('ERROR: No token received during SAML SSO login process.')

            // As a fallback approach we proceed to login screen.
            this.ngZone.run(() => { this.route.navigateByUrl('/login') })
          }
          break;

        case LoginListenerType.app_requires_login:
          this.ngZone.run(() => { this.route.navigateByUrl('/login') })
          break;

        case LoginListenerType.login_success:
          this.displayLandingPage();
          break;
      }
    } catch (error) {
      this.unviredSDK.logError('data service', 'initializeLoginProcces()', 'ERROR: ' + error);
    }
  }

  displayLandingPage() {
    this.dataService.isStartCustomization = true;
    this.ngZone.run(() => { this.route.navigateByUrl('/home') })
  }

  /**
   * Remove /UMP at the end from the url to return as redirect URL
   * @param url the UMP URL. For example: https://inspdev.carmeuse.com/UMP
   * @returns The redirect URL as configured in the UMP. In UMP we configure the redirect URL same as the default URL but without the /UMP appended.
   * For the above input, the return value would be https://inspdev.carmeuse.com
  */
  getRedirectURL(url) {
    let redirectURL = url.substring(0, url.indexOf("/UMP"));
    return redirectURL
  }

  async carmeuseLogin(loginParameters: LoginParameters) {

    loginParameters.url = this.dataService.getUmpUrl()
    loginParameters.company = this.dataService.getCompany();
    loginParameters.jwtOptions = { app: 'PERMIT', language: this.language };
    loginParameters.cacheWebData = true;
    loginParameters.loginType = LoginType.saml2;
    loginParameters['redirectURL'] = this.getRedirectURL(loginParameters.url)

    let authenticateResult = await this.unviredSDK.authenticateAndActivate(loginParameters)
    if (authenticateResult.type == AuthenticateAndActivateResultType.auth_activation_success) {

      // Adding this user to Carmeuse Agent
      // All Carmeuse Internal users belong to Carmeuse agent.
      let settingsResult: SettingsResult = await this.unviredSDK.userSettings();

      await this.presentLoading();
      
      if (settingsResult) {
        let userId: any = settingsResult.data.USER_ID
        
        if (userId) {
          let customData = {
            "USER": [
              {
                "USER_HEADER": { USER_ID: userId }
              }
            ]
          }
          let res: any = await this.dataService.getUserById(customData);
          if (res.type == 0 && Object.keys(res.data).length > 0) {
            this.displayLandingPage()
          } else {
            // Caling create user 
            let userObj = {} as USER_HEADER;
            userObj.P_MODE = 'A';
            userObj.ROLE_NAME = 'ADMIN';
            userObj.FIRST_NAME = settingsResult.data.FULL_NAME.split(' ')[0]
            userObj.LAST_NAME = settingsResult.data.FULL_NAME.split(' ')[1]
            userObj.EMAIL = settingsResult.data.EMAIL
            userObj.USER_ID = settingsResult.data.USER_ID
            userObj.PHONE = ''

            let customData = {
              "AGENT": this.dataService.getCompany(),
              "USERS": [
                userObj
              ]
            }
            let result: any = await this.dataService.createAgentUser(customData);
            if (result && result.type == 0) {
              this.displayLandingPage()
            }
          }
        } 
      } 

      this.loadingController?.dismiss();
    }
    console.log('Auth Result: ' + JSON.stringify(authenticateResult, null, 2))
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      message: 'Please wait...',
    });
    await loading.present();
  }

}
