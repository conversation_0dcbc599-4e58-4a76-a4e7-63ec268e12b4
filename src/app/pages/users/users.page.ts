import { Compo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { ROLE_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { EditUserComponent } from 'src/app/components/edit-user/edit-user.component';

@Component({
  selector: 'app-users',
  templateUrl: './users.page.html',
  styleUrls: ['./users.page.scss'],
})
export class UsersPage implements OnInit, OnDestroy {
  public data: any;
  public usersData: any;
  public usersCount: boolean;
  public agentName: string;
  public agentId: string;
  public isAgentIsInternal: boolean;
  public userRoles: ROLE_HEADER;
  public csvData: any;
  public csvFileContentAsJson: any = [];
  public messageColor: string;
  public searchTermfilter: string = '';
  public filteredData: any = [];
  public selectedAgentId: any;
  private subscriptions = new Subscription();
  showSearchBar = false;
  searchTerm: string = '';
  isLoading: boolean = false;

  constructor(
    public modalCtrl: ModalController,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private dataService: DataService,
    private loadingController: LoadingController,
    public translate: TranslateService,
    public alertController: AlertController,
    private ngZone: NgZone
  ) {}
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  async ngOnInit() {
    this.isLoading = true;
    let subs = this.activatedRoute.queryParams.subscribe(async (params) => {
      this.selectedAgentId = params['agentId'];
      await this.getUsersServerData();
      await this.setFilteredItems();
      this.isLoading = false;
    });

    this.subscriptions.add(subs);
  }

  setFilteredItems() {

    if (this.searchTermfilter.length > 0) {
      this.filteredData = this.usersData.filter((item) => {
        return (
          item.USER_ID.toLowerCase().indexOf(this.searchTermfilter.toLowerCase()) >
            -1 ||
          item.FIRST_NAME.toLowerCase().indexOf(this.searchTermfilter.toLowerCase()) >
            -1 ||
          item.LAST_NAME.toLowerCase().indexOf(this.searchTermfilter.toLowerCase()) >
            -1 ||
          item.EMAIL.toLowerCase().indexOf(this.searchTermfilter.toLowerCase()) > -1
        );
      });
      this.filteredData =
        this.filteredData.length > 0 ? this.filteredData : this.usersData;
    } else {
      this.filteredData = this.usersData;
    }
  }

  async getUsersServerData() {
    this.isLoading = true;
    let userAgent = await this.dataService.getData('USER_AGENT');
    let customData = {};
    if (this.selectedAgentId) {
      customData = {
        AGENT_USER: [
          {
            AGENT_USER_HEADER: {
              AGENT_ID: this.selectedAgentId,
              USER_ID: '',
              P_MODE: '',
            },
          },
        ],
      };
    } else {
      customData = {
        AGENT_USER: [
          {
            AGENT_USER_HEADER: {
              AGENT_ID: userAgent[0] ?userAgent[0].AGENT_ID : "",
              USER_ID: '',
              P_MODE: '',
            },
          },
        ],
      };
    }

    let res = await this.dataService.getAgentUser(customData);
    console.log('agent user data from server', res);

    // Also refresh general user data to ensure consistency
    await this.dataService.getAllUserData();

    // Ensure database operations are complete before querying
    if (res && res.type === ResultType.success) {
      // Add a small delay to ensure database save is complete
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    await this.getUsersDbData();
  }

  async getUsersDbData() {
    let userAgent = await this.dataService.getData('USER_AGENT');
    this.agentId = userAgent[0]? userAgent[0].AGENT_ID : "";

    if (this.selectedAgentId) {
      let agentHeader = await this.dataService.getData(
        'AGENT_HEADER',
        `AGENT_ID = '${this.selectedAgentId}'`
      );
      this.isAgentIsInternal = JSON.parse(agentHeader[0].IS_INTERNAL);
    } else {
      if(userAgent[0])
      this.isAgentIsInternal = JSON.parse(userAgent[0].IS_INTERNAL);
    }

    this.usersData = [];
    if (this.selectedAgentId) {
      let name = await this.dataService.executeQuery(
        `select NAME as NAME  from AGENT_HEADER where agent_id = '${this.selectedAgentId}'`
      );
      this.agentName = name[0]?.NAME;
      this.usersData = await this.dataService
        .executeQuery(`SELECT USER_HEADER.* FROM USER_HEADER JOIN AGENT_USER_HEADER
      ON AGENT_USER_HEADER.USER_ID = USER_HEADER.USER_ID WHERE AGENT_USER_HEADER.AGENT_ID = '${this.selectedAgentId}'`);
    } else {
      this.agentName = userAgent[0]? userAgent[0].NAME: ""
      this.usersData = await this.dataService
        .executeQuery(`SELECT USER_HEADER.* FROM USER_HEADER JOIN AGENT_USER_HEADER
      ON AGENT_USER_HEADER.USER_ID = USER_HEADER.USER_ID WHERE AGENT_USER_HEADER.AGENT_ID = '${userAgent[0] ? userAgent[0].AGENT_ID : ""}'`);
    }

    this.usersCount = this.usersData.length;
    console.log('users data from db', this.usersData);
    if (this.isAgentIsInternal) {
      this.userRoles = await this.dataService
        .executeQuery(`SELECT ROLE_NAME FROM ROLE_HEADER WHERE
     ROLE_HEADER.IS_INTERNAL= '${this.isAgentIsInternal}'`);
      console.log('Available Roles', this.userRoles);
    } else {
      this.userRoles = await this.dataService
        .executeQuery(`SELECT ROLE_NAME FROM ROLE_HEADER WHERE
      ROLE_HEADER.IS_INTERNAL IS NULL OR ROLE_HEADER.IS_INTERNAL = '${this.isAgentIsInternal}'`);
      console.log('Available Roles', this.userRoles);
    }
    this.usersData = this.usersData;
    this.filteredData = this.usersData;
    console.log('getUsersDbData completed - users count:', this.usersData.length);
    this.isLoading = false;
  }

  async uploadFiles(files) {
    if (files.length > 0) {
      this.isLoading = true;
      var reader = new FileReader();
      reader.readAsText(files[0]);
      reader.onload = async (_event) => {
        this.csvData = reader.result;
        console.log('uploaded file data', this.csvData);
        this.csvFileContentAsJson = [];
        this.csvData.split('\n').forEach((element) => {
          var a = element.split(',');
          if (a.length == 6) {
            console.log(a);
            let json = {
              USER_ID: a[0].replace(/(\r\n|\n|\r|\")/g, ''),
              FIRST_NAME: a[1].replace(/(\r\n|\n|\r|\")/g, ''),
              LAST_NAME: a[2].replace(/(\r\n|\n|\r|\")/g, ''),
              PHONE: a[3].replace(/(\r\n|\n|\r|\")/g, ''),
              EMAIL: a[4].replace(/(\r\n|\n|\r|\")/g, ''),
              ROLE_NAME: a[5].replace(/(\r\n|\n|\r|\")/g, ''),
            };
            this.csvFileContentAsJson.push(json);
          }
        });

        let customData = {
          AGENT: this.selectedAgentId ? this.selectedAgentId : this.agentId,
          USERS: this.csvFileContentAsJson,
        };
        console.log('csv data as JSON', customData);
        let result: any = await this.dataService.createAgentUsers(customData);
        console.log('bulk user adding response', result);

        await this.getUsersServerData();

        let message = '';
        if (result) {
          if (result.type == ResultType.success) {
            if (result.data.InfoMessage === undefined) {
              message = this.translate.instant('User Added Successfully');
            } else if (result.data.InfoMessage.length > 0) {
              message = this.translate.instant(result.message.trim());
            }
          }
          if (result.type == ResultType.error) {
            if (result.data.InfoMessage.length > 0) {
              message = this.translate.instant(result.message.trim());
            }
          }
        }

        if (message) {
          await this.alertforUserCreation(message);
        }
      };
    }
  }

  async resetPassword(email) {
    this.dataService
      .sendResetMail(email)
      .toPromise()
      .then(
        async (response) => {
          this.messageColor = 'success';
          if (response.status === 204) {
            this.ngZone.run(async () => {
              await this.alertforUserCreation(
                this.translate.instant(
                  `The reset password link has been sent to ${email}`
                )
              );
            });
          } else {
            await this.alertforUserCreation(
              this.translate.instant(`${email} is not configured properly.`)
            );
          }
        },
        async (error: any) => {
          console.log('API ERROR', error);
          await this.alertforUserCreation(
            this.translate.instant(
              `Something went wrong, please check your email ${email} and try again.`
            )
          );
          this.messageColor = 'danger';
        }
      );
  }

  async addUpdateModal(selectedUser?) {
    // Create a deep copy of the user object to prevent background modifications
    let userCopy = null;
    if (selectedUser) {
      userCopy = JSON.parse(JSON.stringify(selectedUser));
    }

    const modal = await this.modalCtrl.create({
      component: EditUserComponent,
      cssClass: 'add-update-modal',
      backdropDismiss: false,
      componentProps: {
        user: userCopy,
        roles: this.userRoles,
        agentId: this.selectedAgentId ? this.selectedAgentId : this.agentId,
      },
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    console.log('user modal dismissed with data:', data);
    console.log('data.status:', data?.status);
    console.log('typeof data:', typeof data);

    if (data && data.status) {
      console.log('User operation successful, refreshing data...');
      this.isLoading = true;

      // Add a small delay to ensure all operations are complete
      await new Promise(resolve => setTimeout(resolve, 300));

      await this.getUsersServerData();
      console.log('Data refresh completed');
      // Toast notification is now shown by the data service, no need to show alert here
    } else {
      console.log('User operation cancelled or failed, data:', data);
    }
  }

  // Display Loading dialog.
  async displayPleaseWaitLoader(messageReceived) {
    const loading = await this.loadingController.create({
      message: messageReceived,
      backdropDismiss: false,
    });
    await loading.present();
  }

  //Alert to show warning
  async alertforUserCreation(messageReceived) {
    const alert = await this.alertController.create({
      message: this.translate.instant(messageReceived),
      buttons: [
        {
          text: this.translate.instant('Okay'),
        },
      ],
    });
    await alert.present();
  }

  downloadCsv(filename, text) {
    var element = document.createElement('a');
    element.setAttribute(
      'href',
      'data:text/csv;charset=utf-8,' + encodeURIComponent(text)
    );
    element.setAttribute('download', filename);

    element.style.display = 'none';
    document.body.appendChild(element);

    element.click();

    document.body.removeChild(element);
  }

  async showHelpButtonClicked() {
    const alert = await this.alertController.create({
      header: this.translate.instant('Importing Users'),
      message: this.translate.instant(
        'You can import users from a CSV file. A sample csv file is given for reference. You can download this file, enter user data according to the columns and import the same by clicking on the button Import Users.'
      ),
      buttons: [
        {
          text: this.translate.instant('Cancel'),
        },
        {
          text: this.translate.instant('Download Sample CSV'),
          handler: () => {
            this.downloadCsv(
              'usersSample.csv',
              'User ID,First Name,Last Name,Phone Number,Email ID,Role'
            );
          },
        },
      ],
    });
    await alert.present();
  }


  filterUsersBasedOnSearch() {
    console.log('Search triggered with term:', this.searchTerm);
    console.log('Users data length:', this.usersData?.length);

    if (!this.usersData || this.usersData.length === 0) {
      console.log('No users data available for search');
      return;
    }

    const search = this.searchTerm?.toLowerCase() || '';
    if(search){
      this.filteredData = this.usersData.filter(user => {
        // Check if username matches the search - add null checks
        const matchesUserID = user.USER_ID?.toLowerCase().includes(search) || false;
        const matchesUserFirstName = user.FIRST_NAME?.toLowerCase().includes(search) || false;
        const matchesUserLastName = user.LAST_NAME?.toLowerCase().includes(search) || false;
        const matchesUserEmail = user.EMAIL?.toLowerCase().includes(search) || false;
        const matchesRole = user.ROLE_NAME?.toLowerCase().includes(search) || false;

        return matchesUserID || matchesUserFirstName || matchesUserLastName || matchesUserEmail || matchesRole ;
      });
      console.log('Filtered results count:', this.filteredData.length);
    } else {
      this.filteredData = this.usersData;
      console.log('Search cleared, showing all users:', this.filteredData.length);
    }
  }

  toggleSearchBar() {
    this.showSearchBar = !this.showSearchBar;
  }

  closeSearchBar() {
    this.searchTerm = ''
    this.showSearchBar = false;
    this.filteredData = this.usersData;
  }

  getRoleBadgeClass(roleName: string): string {
    // Convert role name to lowercase for case-insensitive comparison
    const role = roleName.toLowerCase();

    if (role.includes('admin')) {
      return 'role-badge-admin';
    } else if (role.includes('manager') || role.includes('supervisor')) {
      return 'role-badge-manager';
    } else if (role.includes('inspector') || role.includes('reviewer')) {
      return 'role-badge-inspector';
    } else if (role.includes('approver') || role.includes('issuer')) {
      return 'role-badge-approver';
    } else if (role.includes('requester') || role.includes('initiator')) {
      return 'role-badge-requester';
    } else {
      return 'role-badge-default';
    }
  }

  formatPhoneNumber(phoneNumber: string): string {
    // Handle null, undefined, or empty phone numbers
    if (!phoneNumber) {
      return '';
    }

    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');

    // Format based on length
    if (digits.length === 10) {
      // Format as (###) ###-####
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    } else if (digits.length === 11 && digits.startsWith('1')) {
      // Handle 11-digit numbers starting with 1 (US country code)
      return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
    } else if (digits.length > 0) {
      // For other lengths, return as-is with original formatting if any
      return phoneNumber;
    }

    return phoneNumber;
  }
}
