/* Background styling for the page */
:host {
  background: linear-gradient(to bottom, rgb(245, 246, 248) 0%, rgb(255, 255, 255) 100%);
  display: block;
  height: 100%;
  overflow-y: auto;
  padding: 10px;
}

/* Table styling */
.users-table {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 0 15px 20px;
  width: calc(100% - 30px);
  table-layout: fixed;
  position: relative;
  box-sizing: border-box;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #e9ecef;
  padding: 10px 16px;
  font-size: 14px;
  height: 48px;
  align-items: center;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  padding: 10px 16px;
  font-size: 14px;
  transition: background-color 0.2s;
  align-items: center;
  height: 56px;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell, .table-cell {
  padding: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.id-cell {
  flex: 1.5;
  min-width: 120px;
}

.name-cell {
  flex: 1.5;
}

.email-cell {
  flex: 2;
}

.phone-cell {
  flex: 1;
}

.role-cell {
  flex: 1;
  display: flex;
  justify-content: center;
}

.actions-cell {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 4px;
}

.actions-cell ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
  height: 32px;
}

.actions-cell ion-button ion-icon {
  font-size: 16px;
}

/* Search card styling */
.search-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  margin: 0 15px 20px;
  width: calc(100% - 30px);
}

.search-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: #f1f3f5;
  border-radius: 8px;
  padding: 6px 12px;
  flex: 1;
  max-width: 300px;
  height: 36px;
}

.search-wrapper ion-icon {
  color: #6c757d;
  margin-right: 8px;
  font-size: 16px;
}

.search-wrapper input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  font-size: 14px;
  color: #495057;
}

/* Agent info section styling */
.agent-info-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  max-width: 300px;
  margin: 0 20px;
}

.agent-name-container {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 8px 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.agent-name-container:hover {
  background-color: #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.agent-icon {
  color: var(--ion-color-primary);
  font-size: 18px;
  margin-right: 10px;
  flex-shrink: 0;
}

.agent-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.agent-label {
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.agent-name {
  font-size: 14px;
  color: #343a40;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons ion-button {
  --padding-start: 12px;
  --padding-end: 12px;
  --padding-top: 0;
  --padding-bottom: 0;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
}

.action-buttons ion-button ion-icon {
  font-size: 16px;
  margin-right: 4px;
}

.add-btn {
  background-color: var(--ion-color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 36px;
}

.add-btn:hover {
  background-color: var(--ion-color-primary-shade);
}

.add-btn ion-icon {
  margin-right: 6px;
  font-size: 16px;
}

.action-btn {
  background-color: #f1f3f5;
  color: #495057;
  border: none;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 36px;
}

.action-btn:hover {
  background-color: #e9ecef;
}

.action-btn ion-icon {
  margin-right: 6px;
  font-size: 16px;
}

.action-btn.secondary {
  background-color: var(--ion-color-secondary);
  color: white;
}

.action-btn.secondary:hover {
  background-color: var(--ion-color-secondary-shade);
}

/* Empty state styling */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
  width: 100%;
}

.empty-state-content {
  text-align: center;
  max-width: 400px;
  padding: 20px;
}

.empty-state-icon {
  font-size: 64px;
  color: #adb5bd;
  margin-bottom: 16px;
}

.empty-state-content h2 {
  font-size: 24px;
  color: #343a40;
  margin-bottom: 8px;
}

.empty-state-content p {
  color: #6c757d;
  margin-bottom: 16px;
}

.empty-state-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* No results message styling */
.no-results-message {
  padding: 40px 0;
  text-align: center;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.no-results-content ion-icon {
  font-size: 48px;
  color: #adb5bd;
}

.no-results-content p {
  color: #6c757d;
  font-size: 16px;
}

/* Clear filters button styling removed - using action-btn instead */

/* Shimmer effect for loading state */
.shimmer-container {
  padding: 20px;
}

.shimmer-search-card {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
}

.shimmer-search-input {
  height: 40px;
  width: 300px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px;
  margin-bottom: 10px;
}

.shimmer-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.shimmer-filter {
  height: 30px;
  width: 100px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 15px;
}

.shimmer-button {
  height: 40px;
  width: 120px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px;
  margin-left: auto;
}

.shimmer-table {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.shimmer-header {
  height: 50px;
  background: linear-gradient(90deg, #f0f0f0 0%, #f8f8f8 50%, #f0f0f0 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.shimmer-row {
  height: 60px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-top: 1px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Role badge styling */
.role-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  background-color: rgba(var(--ion-color-primary-rgb), 0.15);
  color: var(--ion-color-primary);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.role-badge-admin {
  background-color: rgba(220, 53, 69, 0.15);
  color: #dc3545; /* Red */
}

.role-badge-manager {
  background-color: rgba(111, 66, 193, 0.15);
  color: #6f42c1; /* Purple */
}

.role-badge-inspector {
  background-color: rgba(23, 162, 184, 0.15);
  color: #17a2b8; /* Cyan */
}

.role-badge-approver {
  background-color: rgba(40, 167, 69, 0.15);
  color: #28a745; /* Green */
}

.role-badge-requester {
  background-color: rgba(255, 193, 7, 0.15);
  color: #fd7e14; /* Orange */
}

.role-badge-default {
  background-color: rgba(108, 117, 125, 0.15);
  color: #6c757d; /* Gray */
}

/* Email link styling */
.email-link {
  color: var(--ion-color-primary);
  text-decoration: none;
  transition: color 0.2s;
}

.email-link:hover {
  color: var(--ion-color-primary-shade);
  text-decoration: underline;
}

/* Image upload styling */
.image-upload {
  input {
    display: none;
  }

  label {
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  &.action-btn label ion-icon,
  &.action-btn.secondary label ion-icon {
    margin-right: 6px;
    font-size: 16px;
  }
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-wrapper {
    max-width: 100%;
    order: 1;
  }

  .agent-info-section {
    order: 2;
    max-width: 100%;
    margin: 0;
    justify-content: center;
  }

  .agent-name-container {
    width: 100%;
    justify-content: center;
    max-width: 280px;
  }

  .agent-details {
    align-items: center;
  }

  .agent-name {
    max-width: 200px;
  }

  .action-buttons {
    justify-content: flex-end;
    order: 3;
  }

  .table-header {
    display: none;
  }

  .table-row {
    flex-direction: column;
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
  }

  .table-cell {
    width: 100%;
    padding: 8px 0;
    text-align: right;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-cell::before {
    content: attr(data-label);
    font-weight: 600;
    text-align: left;
    padding-right: 10px;
  }

  .actions-cell {
    justify-content: flex-end;
  }
}