ion-content {
    --padding-start: 24px;
    --padding-end: 24px;
    --padding-bottom: 24px;
    --padding-top: 15%;

    #logo {
        text-align: center;

        img {
            width: 240px;
        }
    }

    #header {
        display: flex;
        flex-direction: column;
        text-align: center;


        h1 {
            font-weight: bold;
            font-size: 18px;
            color: #162F43;
            margin-bottom: 4px;
            line-height: 24px;
        }

        p {
            font-size: 14px;
            color: #406882;
            margin-bottom: 32px;
            line-height: 16px;
        }
    }

    #form {
        #email {
            margin-bottom: 16px;
        }

        #password {
            margin-bottom: 24px;
        }

        #forgotPassword {
            text-align: right;

            p {
                font-size: 14px;
                color: #1A374D;
                margin-bottom: 24px;
                line-height: 24px;
                text-decoration: underline;
            }
        }
    }

    #password-reset-form {
        #new-password {
            margin-bottom: 16px;
        }

        #confirm-password {
            margin-bottom: 16px;
        }

        #reset-token {
            margin-bottom: 24px;
        }

        #change-password-button {
            margin-bottom: 16px;
        }

        #back-to-login {
            text-align: center;
        }
    }

    #login-with {
        margin-top: 32px;
        margin-bottom: 32px;

        h2 {
            width: 100%;
            text-align: center;
            border-bottom: 1px solid #ADCBD7;
            line-height: 0.1em;
            margin: 10px 0 20px;
        }

        h2 span {
            background: #fff;
            padding: 0 10px;
            color: #406882;
            font-size: 14px;
        }
    }

    #version {
        p {
            font-size: 14px;
            color: #6b6c6d;
            line-height: 16px;
            position: absolute;
            bottom: 0;
            width: 90%;
            text-align: center;
        }
    }

    .textCenter {
        text-align: center;
    }
}