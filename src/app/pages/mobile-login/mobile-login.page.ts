import { Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthenticateActivateResult, AuthenticateAndActivateResultType, AuthenticateLocalResult, AuthenticateLocalResultType, LoginListenerType, LoginParameters, LoginType, SettingsResult, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController, LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { USER_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { AppConstants } from 'src/app/shared/app-constants';

@Component({
  selector: 'app-mobile-login',
  templateUrl: './mobile-login.page.html',
  styleUrls: ['./mobile-login.page.scss'],
})
export class MobileLoginPage implements OnInit {
  public constants: AppConstants;
  public versionHide: boolean = true;
  public appVersion: string = '';
  public emailId: string = '';
  public password: string = '';
  public loginParameters: LoginParameters = null;
  public loginResultType: LoginListenerType = null;
  public company: string = '';
  public errorMessage: string = '';
  public showErrorMessage: boolean = false;
  public messageColor: string;
  public progressbar: boolean = false;
  public selectedUrl: string;
  public busyIndicator: any = null;
  public fieldTextType: boolean = false;

  // Forgot password fields
  public showPasswordResetForm: boolean = false;
  public newPassword: string = '';
  public confirmPassword: string = '';
  public resetToken: string = '';
  
  constructor(
    private dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    private route: Router,
    public loadingController: LoadingController,
    public translate: TranslateService,
    private ngZone: NgZone,
    public alertController: AlertController) { }

  ngOnInit() {
    window.addEventListener('keyboardDidHide', () => {
      this.versionHide = true;
    });
    window.addEventListener('keyboardWillShow', () => {
      this.versionHide = false;
    });
    this.constants = new AppConstants();
    this.loginResultType = this.dataService.loginResultType;
    let url = location.href;
    this.selectedUrl = this.dataService.getUmpUrl(url);
    console.log('URL :  ' + this.selectedUrl);
    this.appVersion = this.dataService.getAppVersion();
  }

  async login() {
    // Busy indicator, when other processes are running
    this.busyIndicator = await this.loadingController.create({
      message: this.translate.instant('Signing in...'),
      spinner: this.translate.instant('crescent'),
      animated: true,
      showBackdrop: true,
      translucent: true,
    });

    if (
      this.selectedUrl != null &&
      this.emailId != null &&
      this.password != null
    ) {
      // Set the arguments for ump
      this.loginParameters = new LoginParameters();
      this.loginParameters.url = this.selectedUrl;
      this.loginParameters.company = this.dataService.getCompany();
      this.loginParameters.loginType = LoginType.email;
      this.loginParameters.username = this.emailId.trim();
      this.loginParameters.password = this.password;
      this.loginParameters.jwtOptions = {
        app: 'PERMIT',
        language: 'en',
      };
      this.loginParameters.cacheWebData = true;

      // send selected language.
      this.loginParameters.loginLanguage = 'en';
      this.unviredSDK.logInfo(
        'Login',
        'login()',
        'Selected Language Code: ' + 'en'
      );
      this.loginResultType = this.loginResultType ?? 0;
      try {
        switch (this.loginResultType) {
          // |Authenticate and Activate|
          case LoginListenerType.auth_activation_required:
            await this.busyIndicator.present();
            let authenticateActivateResult: AuthenticateActivateResult;
            try {
              authenticateActivateResult =
                await this.unviredSDK.authenticateAndActivate(
                  this.loginParameters
                );
              // |Authentication activated and Login success|
              if (
                authenticateActivateResult.type ===
                AuthenticateAndActivateResultType.auth_activation_success
              ) {
                // this.setLocalDataToStorage();
                this.busyIndicator.dismiss();
                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateActivateResult.type ===
                AuthenticateAndActivateResultType.auth_activation_error
              ) {
                this.busyIndicator.dismiss();
                this.showErrorMessage = true;
                this.errorMessage = authenticateActivateResult.error;
                this.messageColor = 'danger';
              }
            } catch (error) {
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateAndActivate()',
                'ERROR: ' + error
              );
            }
            break;

          // |Authenticate Locally|
          case LoginListenerType.app_requires_login:
            this.busyIndicator.present();
            let authenticateLocalResult: AuthenticateLocalResult;
            try {
              authenticateLocalResult = await this.unviredSDK.authenticateLocal(
                this.loginParameters
              );
              // |Authenticate (Local) credentials saved in database|
              if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_success
              ) {
                // this.setLocalDataToStorage();
                this.busyIndicator.dismiss();
                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_error
              ) {
                this.busyIndicator.dismiss();
                this.presentAlert(authenticateLocalResult.error);
              }
            } catch (error) {
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateLocal()',
                'ERROR: ' + error
              );
            }
            break;
        }
      } catch (error) {
        this.unviredSDK.logError('LoginPage', 'login()', 'ERROR: ' + error);
      }
    }
  }

  // Navigate to landing page
  displayLandingPage() {
    // Flag for do customization only when user navigates from login page.
    this.loadingController?.dismiss();
    this.dataService.isStartCustomization = true;
    this.route.navigate(['mobile-home']);
  }

  // Alert, when get any error response from ump
  async presentAlert(errorResponse: string) {
    let showAlert = await this.alertController.create({
      header: this.translate.instant('Error'),
      message: errorResponse,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
        },
      ],
    });
    await showAlert.present();
  }

  forgetPassword(event) {
    event.preventDefault();

    //validate Email Id
    if (!this.emailId) {
      // Email Id is not present
      //display message asking for Email Id
      this.showErrorMessage = true;
      this.progressbar = false;
      this.errorMessage = this.translate.instant(
        'Email Id is required to reset password'
      );
      this.messageColor = 'danger';
      return;
    }

    //Email Id valid, disable previous error messages if any
    console.log('Email Id is valid make api call');
    this.showErrorMessage = false;
    this.errorMessage = '';
    this.progressbar = true;
    //Email Id valid, make api call
    let EmailId = this.emailId;
    this.dataService
      .sendResetMail(EmailId)
      .toPromise()
      .then(
        async (response) => {
          this.messageColor = 'success';
          this.progressbar = false;
          if (response.status === 204) {
            this.ngZone.run(() => {
              this.showPasswordResetForm = true;
              this.showErrorMessage = true;
              this.errorMessage = this.translate.instant(
                'A reset token has been sent to your email. Please enter the token along with your new password below.'
              );
            });
          } else {
            this.showErrorMessage = true;
            this.errorMessage = this.translate.instant(
              'Email is not configured. You may want to create account first.'
            );
          }
        },
        (error: any) => {
          console.log('API ERROR', error);
          this.showErrorMessage = true;
          this.progressbar = false;
          this.errorMessage = this.translate.instant(
            'Something went wrong please check company or email and try again.'
          );
          this.messageColor = 'danger';
        }
      );
  }

  changePassword(event) {
    event.preventDefault();

    // Validate required fields
    if (!this.newPassword || !this.confirmPassword || !this.resetToken) {
      this.showErrorMessage = true;
      this.progressbar = false;
      this.errorMessage = this.translate.instant(
        'All fields are required to change password'
      );
      this.messageColor = 'danger';
      return;
    }

    // Validate password match
    if (this.newPassword !== this.confirmPassword) {
      this.showErrorMessage = true;
      this.progressbar = false;
      this.errorMessage = this.translate.instant(
        'New password and confirm password do not match'
      );
      this.messageColor = 'danger';
      return;
    }

    // Validate password strength (optional - add your own rules)
    if (this.newPassword.length < 6) {
      this.showErrorMessage = true;
      this.progressbar = false;
      this.errorMessage = this.translate.instant(
        'Password must be at least 6 characters long'
      );
      this.messageColor = 'danger';
      return;
    }

    // All validations passed, disable previous error messages
    console.log('All validations passed, making change password API call');
    this.showErrorMessage = false;
    this.errorMessage = '';
    this.progressbar = true;

    // Make API call to change password
    this.dataService
      .changePasswordWithToken(this.emailId, this.newPassword, this.resetToken)
      .toPromise()
      .then(
        async (response) => {
          this.progressbar = false;
          if (response.status === 200 || response.status === 204) {
            // Success - navigate back to login with success message
            this.ngZone.run(async () => {
              // Show success alert
              const successAlert = await this.alertController.create({
                header: this.translate.instant('Success'),
                message: this.translate.instant(
                  'Your password has been changed successfully. Please login with your new password.'
                ),
                animated: true,
                backdropDismiss: false,
                buttons: [
                  {
                    text: this.translate.instant('OK'),
                    handler: () => {
                      // Reset form and go back to login
                      this.resetPasswordForm();
                    }
                  },
                ],
              });
              await successAlert.present();
            });
          } else {
            this.showErrorMessage = true;
            this.errorMessage = this.translate.instant(
              'Failed to change password. Please try again.'
            );
            this.messageColor = 'danger';
          }
        },
        (error: any) => {
          console.log('Change Password API ERROR', error);
          this.showErrorMessage = true;
          this.progressbar = false;
          let errorMsg = 'Something went wrong while changing password. Please try again.';

          // Handle specific error messages
          if (error.status === 400) {
            errorMsg = 'Invalid token or password. Please check your inputs.';
          } else if (error.status === 404) {
            errorMsg = 'User not found. Please check your email address.';
          } else if (error.status === 401) {
            errorMsg = 'Invalid or expired token. Please request a new reset token.';
          }

          this.errorMessage = this.translate.instant(errorMsg);
          this.messageColor = 'danger';
        }
      );
  }

  resetPasswordForm() {
    this.showPasswordResetForm = false;
    this.newPassword = '';
    this.confirmPassword = '';
    this.resetToken = '';
    this.showErrorMessage = false;
    this.errorMessage = '';
    this.messageColor = '';
  }

  onChangeDisableErrorMsg(event) {
    this.showErrorMessage = event.target.value;
    this.showErrorMessage = false;
  }

  getRedirectURL(url: any) {
    let redirectURL = url.substring(0, url.indexOf('/UMP'));
    return redirectURL;
  }

  async internalLogin() {
    await this.presentLoading();
    let loginParameters = new LoginParameters();
    loginParameters.url = this.dataService.getUmpUrl();
    loginParameters.company = this.dataService.getCompany();

    loginParameters.jwtOptions = { app: 'PERMIT' };
    loginParameters.cacheWebData = true;

    // For SAML Login
    loginParameters.loginType = LoginType.saml2;
    loginParameters['redirectURL'] = this.dataService.getUmpUrl();

    let authenticateResult = await this.unviredSDK.authenticateAndActivate(
      loginParameters
    );
    if (
      authenticateResult.type ==
      AuthenticateAndActivateResultType.auth_activation_success
    ) {
      let settingsResult: SettingsResult = await this.unviredSDK.userSettings();
      if (settingsResult) {
        let userId: any = settingsResult.data.USER_ID;

        if (userId) {
          let customData = {
            USER: [
              {
                USER_HEADER: { USER_ID: userId },
              },
            ],
          };
          let res: any = await this.dataService.getUserById(customData);
          if (res.type == 0 && Object.keys(res.data).length > 0) {
            this.displayLandingPage();
          } else {
            // Caling create user
            let userObj = {} as USER_HEADER;
            userObj.P_MODE = 'A';
            userObj.ROLE_NAME = 'ADMIN';
            userObj.FIRST_NAME = settingsResult.data.FULL_NAME.split(' ')[0];
            userObj.LAST_NAME = settingsResult.data.FULL_NAME.split(' ')[1];
            userObj.EMAIL = settingsResult.data.EMAIL;
            userObj.USER_ID = settingsResult.data.USER_ID;
            userObj.PHONE = '';

            let customData = {
              AGENT: this.dataService.getCompany(),
              USERS: [userObj],
            };
            let result: any = await this.dataService.createAgentUser(
              customData
            );
            if (result && result.type == 0) {
              this.displayLandingPage();
            }
          }
        }
      } else {
        this.loadingController?.dismiss();
      }
    } else {
      this.loadingController?.dismiss();
    }
    console.log('Auth Result: ' + JSON.stringify(authenticateResult, null, 2));
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      message: 'Please wait...',
    });
    await loading.present();
  }
}
