<ion-content [fullscreen]="true">
  <div id="logo">
    <img src="../../../assets/images/unvired.png" />
  </div>

  <div id="header">
    <h1>Login to Safe Work Permit</h1>
    <p>We're happy to see you back again!</p>
  </div>

  <ion-progress-bar type="indeterminate" *ngIf="progressbar" style="margin-bottom: 20px;"></ion-progress-bar>

  <div id="form" *ngIf="!showPasswordResetForm">
    <div id="email">
      <ion-input mode="md" [(ngModel)]="emailId" placeholder="Enter email" style="--padding-start: 16px !important;"
        labelPlacement="stacked" fill="outline" (ionChange)="onChangeDisableErrorMsg($event)">
        <div slot="label">E-mail</div>
        <ion-icon slot="start" name="mail-outline" style="color: #1A374D;font-size: 25px;"></ion-icon>
      </ion-input>
    </div>
    <div id="password">
      <ion-input mode="md" [(ngModel)]="password" placeholder="Enter password" [type]="fieldTextType ? 'text' : 'password'"
        style="--padding-start: 16px !important;" labelPlacement="stacked" fill="outline"
        (ionChange)="onChangeDisableErrorMsg($event)">
        <div slot="label">Password</div>
        <ion-icon slot="start" style="color: #1A374D;font-size: 25px;" name="lock-closed-outline"></ion-icon>
        <ion-button fill="clear" slot="end" aria-label="Show/hide" (click)="fieldTextType = !fieldTextType">
          <ion-icon slot="icon-only" [name]="fieldTextType ? 'eye-outline': 'eye-off-outline'" style="color: #9CB1BA" aria-hidden="true"></ion-icon>
        </ion-button>
      </ion-input>
    </div>
    <div id="forgotPassword">
      <p (click)="forgetPassword($event)">Forgotten Password</p>
    </div>

    <div id="login-button">
      <ion-button style="font-size: medium;" expand="full" size="large"
        (click)="(!selectedUrl || !emailId || !password) ? '': login()">Login</ion-button>
    </div>
  </div>

  <!-- Password Reset Form -->
  <div id="password-reset-form" *ngIf="showPasswordResetForm">
    <div id="new-password" style="margin-bottom: 16px;">
      <ion-input mode="md" [(ngModel)]="newPassword" type="password" placeholder="Enter new password"
        style="--padding-start: 16px !important;" labelPlacement="stacked" fill="outline"
        (ionChange)="onChangeDisableErrorMsg($event)">
        <div slot="label">New Password</div>
        <ion-icon slot="start" style="color: #1A374D;font-size: 25px;" name="lock-closed-outline"></ion-icon>
      </ion-input>
    </div>

    <div id="confirm-password" style="margin-bottom: 16px;">
      <ion-input mode="md" [(ngModel)]="confirmPassword" type="password" placeholder="Confirm new password"
        style="--padding-start: 16px !important;" labelPlacement="stacked" fill="outline"
        (ionChange)="onChangeDisableErrorMsg($event)">
        <div slot="label">Confirm Password</div>
        <ion-icon slot="start" style="color: #1A374D;font-size: 25px;" name="lock-closed-outline"></ion-icon>
      </ion-input>
    </div>

    <div id="reset-token" style="margin-bottom: 24px;">
      <ion-input mode="md" [(ngModel)]="resetToken" placeholder="Enter token from email"
        style="--padding-start: 16px !important;" labelPlacement="stacked" fill="outline"
        (ionChange)="onChangeDisableErrorMsg($event)">
        <div slot="label">Reset Token</div>
        <ion-icon slot="start" style="color: #1A374D;font-size: 25px;" name="key-outline"></ion-icon>
      </ion-input>
    </div>

    <div id="change-password-button">
      <ion-button style="font-size: medium;" expand="full" size="large"
        [disabled]="!newPassword || !confirmPassword || !resetToken"
        (click)="changePassword($event)">Change Password</ion-button>
    </div>

    <div id="back-to-login" style="margin-top: 16px;">
      <ion-button fill="clear" expand="full" (click)="resetPasswordForm()">
        Back to Login
      </ion-button>
    </div>
  </div>

  <div id="login-with">
    <h2><span>Or login with</span></h2>
  </div>

  <div>
    <ion-button (click)="internalLogin()" style="font-size: medium;" size="large" expand="full" color="light">Unvired
      SSO</ion-button>
  </div>

  <div id="version" *ngIf="versionHide">
    <p>Version {{appVersion}}</p>
  </div>

  <div style="margin-top: 10%;text-align: center;">
    <ion-text [color]="messageColor" class="textCenter" *ngIf="showErrorMessage"
      (ionChange)="onChangeDisableErrorMsg($event)">
      <span>{{errorMessage}}</span>
    </ion-text>
  </div>
</ion-content>