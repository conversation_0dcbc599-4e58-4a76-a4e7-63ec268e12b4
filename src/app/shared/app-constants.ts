export class AppConstants {
  // Constants
  public APP_RELEASE_NUMBER = '99.99.99';
  public APP_RELEASE_DATE = '@@RELEASE_DATE@@';
  public static APP_NAME = 'PERMIT';
  public DEFAULT_UMP_URL = 'https://umpdev.unvired.io';
  public static ATTACHMENT_URL = '/UMP/API/v2/applications/';
  public DEFAULT_LOGIN_TYPE = 'email';
  public DOUBLE_TAP_IGNORE_DURATION = 2000;
  public static PERMITS_LIMIT = 10;
  public static YES = "X";
  public static NO = "";

  // UMP Types
  public PRODUCTION = 'P';
  public QUALITY = 'Q';
  public DEVELOPMENT = 'D';

// BE Names
public static BE_AGENT = "AGENT";
public static BE_AGENT_USER = "AGENT_USER";
public static BE_APPROVAL_TYPE = "APPROVAL_TYPE";
public static BE_DIVISION = "DIVISION";
public static BE_DOCUMENT = "DOCUMENT";
public static BE_FACILITY = "FACILITY";
public static BE_FORM = "FORM";
public static BE_INPUT_GET_FORM_TEMPLATE = "INPUT_GET_FORM_TEMPLATE";
public static BE_PERMIT = "PERMIT";
public static BE_PERMIT_QUERY_CTX = "PERMIT_QUERY_CTX";
public static BE_PERMIT_TYPE = "PERMIT_TYPE";
public static BE_ROLE = "ROLE";
public static BE_STRUCTURE = "STRUCTURE";
public static BE_STRUCTURE_CAT = "STRUCTURE_CAT";
public static BE_STRUCTURE_STATUS = "STRUCTURE_STATUS";
public static BE_STRUCTURE_TYPE = "STRUCTURE_TYPE";
public static BE_USER = "USER";
public static BE_USER_APPROVAL_TYPE = "USER_APPROVAL_TYPE";
public static BE_USER_CONTEXT = "USER_CONTEXT";
public static BE_SKILL = "SKILL"


// PA Function Names
public static PA_BULK_UPLOAD_STRUCTURE = "PERMIT_PA_BULK_UPLOAD_STRUCTURE"; // Bulk upload structure
public static PA_CREATE_USER = "PERMIT_PA_CREATE_USER"; // Create User
public static PA_GET_AGENT = "PERMIT_PA_GET_AGENT"; // Get Agent
public static PA_GET_AGENT_USER = "PERMIT_PA_GET_AGENT_USER"; // Get Agent User
public static PA_GET_CUSTOMIZATION = "PERMIT_PA_GET_CUSTOMIZATION"; // Get Customization
public static PA_GET_DIVISION = "PERMIT_PA_GET_DIVISION"; // Get Division
public static PA_GET_DOCUMENT = "PERMIT_PA_GET_DOCUMENT"; // Get Document
public static PA_GET_FACILITY = "PERMIT_PA_GET_FACILITY"; // Get Facility
public static PA_GET_FORM_TEMPLATES = "PERMIT_PA_GET_FORM_TEMPLATES"; // Get Templates from Forms App
public static PA_GET_PERMIT = "PERMIT_PA_GET_PERMIT"; // Get Permit
public static PA_GET_PERMIT_TYPE = "PERMIT_PA_GET_PERMIT_TYPE"; // Get Permit Type
public static PA_GET_STRUCTURE = "PERMIT_PA_GET_STRUCTURE"; // Get Structure
public static PA_GET_STRUCTURE_CATEGORY = "PERMIT_PA_GET_STRUCTURE_CATEGORY"; // Get Structure Category
public static PA_GET_STRUCTURE_STATUS = "PERMIT_PA_GET_STRUCTURE_STATUS"; // Get Structure Status
public static PA_GET_STRUCTURE_TYPE = "PERMIT_PA_GET_STRUCTURE_TYPE"; // Get Structure Type
public static PA_GET_USER = "PERMIT_PA_GET_USER"; // Get User
public static PA_GET_USER_APPROVAL_TYPE = "PERMIT_PA_GET_USER_APPROVAL_TYPE"; // No desc available
public static PA_GET_USER_CONTEXT = "PERMIT_PA_GET_USER_CONTEXT"; // Get User Context
public static PA_GET_SKLL = "PERMIT_PA_GET_SKILL"; // Get skill
public static PA_MODIFY_AGENT = "PERMIT_PA_MODIFY_AGENT"; // Modify Agent
public static PA_MODIFY_AGENT_USER = "PERMIT_PA_MODIFY_AGENT_USER"; // Modify Agent User
public static PA_MODIFY_DIVISION = "PERMIT_PA_MODIFY_DIVISION"; // Modify Division
public static PA_MODIFY_DOCUMENT = "PERMIT_PA_MODIFY_DOCUMENT"; // Modify Document
public static PA_MODIFY_FACILITY = "PERMIT_PA_MODIFY_FACILITY"; // Modify Facility
public static PA_MODIFY_PERMIT = "PERMIT_PA_MODIFY_PERMIT"; // Modify Permit
public static PA_MODIFY_PERMIT_TYPE = "PERMIT_PA_MODIFY_PERMIT_TYPE"; // Modify Permit Type
public static PA_MODIFY_ROLE = "PERMIT_PA_MODIFY_ROLE"; // Modify Role
public static PA_MODIFY_STRUCTURE = "PERMIT_PA_MODIFY_STRUCTURE"; // Modify Structure
public static PA_MODIFY_STRUCTURE_CATEGORY = "PERMIT_PA_MODIFY_STRUCTURE_CATEGORY"; // Modify Structure Category
public static PA_MODIFY_STRUCTURE_STATUS = "PERMIT_PA_MODIFY_STRUCTURE_STATUS"; // Modify Structure Status
public static PA_MODIFY_STRUCTURE_TYPE = "PERMIT_PA_MODIFY_STRUCTURE_TYPE"; // Modify Structure Type
public static PA_MODIFY_USER = "PERMIT_PA_MODIFY_USER"; // Modify User
public static PA_MODIFY_USER_APPROVAL_TYPE = "PERMIT_PA_MODIFY_USER_APPROVAL_TYPE"; // No desc available
public static PA_MODIFY_USER_CONTEXT = "PERMIT_PA_MODIFY_USER_CONTEXT"; // Set User Context
public static PA_MODIFY_SKILL = "PERMIT_PA_MODIFY_SKILL"
 public PERMIT_FORM = "PERMIT_FORM"; 
 public PERMIT_HEADER = "PERMIT_HEADER";
 public static PA_PERMIT_PA_MODIFY_PERMIT = "PERMIT_PA_MODIFY_PERMIT"; // Set User Context
 public static PA_PERMIT_PA_REP_GET_PERMIT = "PERMIT_PA_REP_GET_PERMIT"; // Set User Context
 public static PA_MODIFY_APPROVAL_TYPE = "PERMIT_PA_MODIFY_APPROVAL_TYPE";
 public static PERMIT_PA_GET_DASHBOARD = 'PERMIT_PA_GET_DASHBOARD';
 // ************** for my reference **************

 public TEAM_MEMBER_TABLE = 'TEAM_MEMBER';
 public TASK_SUBMISSION_ATTACHMENT_TABLE = 'TASK_SUBMISSION_ATTACHMENT';
 public FORM_DOCUMENTS_ATTACHMENT_TABLE = 'FORM_DOCUMENTS_ATTACHMENT';
//  public SETTINGS_TABLE = 'SETTINGS_HEADER';
 public PA_MOBILE_GET_FORM_DETAILS_FROM_TASK = 'DIGITAL_FORMS_PA_MOBILE_GET_FORM_DETAILS_FROM_TASK';
 public FORM_TABLE = 'FORM_HEADER';
 public MASTER_DATA_TABLE = 'MASTER_DATA_HEADER';
 public TASK_SUBMISSION_TABLE = 'TASK_SUBMISSION_HEADER';
 public TASK_USER_TABLE = 'TASK_USER_HEADER';
 public TASK_USER_BE = 'TASK_USER';
 public PA_UPDATE_TASK_USER = 'DIGITAL_FORMS_PA_MOBILE_UPDATE_TASK_USER'; // Download/Update Task User
 public TASK_SUBMISSION_BE = 'TASK_SUBMISSION';
 public PA_UPDATE_TASK_SUBMISSION = 'DIGITAL_FORMS_PA_MOBILE_UPDATE_TASK_SUBMISSION'; // Download/Update Submission
 public PRIORITY_TABLE = 'PRIORITY_HEADER';
 public TASK_ALERT_TABLE = 'TASK_ALERT_HEADER';
 public PERMIT_QUERY_CTX_HEADER = 'PERMIT_QUERY_CTX_HEADER';
 public TASK_STATUS = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN PROGRESS',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  DELEGATED: 'DELEGATED',
  APPROVAL_STATUS: 'APPROVAL_STATUS'
};
public TASK_USER_ACCESS = {
  EDIT: '0',
  READ_ONLY: '1'
};

public TASK_TYPE = {
  FILL: 'FILL',
  VIEW: 'VIEW',
  REVIEW: 'REVIEW',
  FEEDBACK: 'FEEDBACK',
  RESOLVE: 'RESOLVE',
  FORMSET: 'FORMSET',
  CHECKOUT: 'CHECKOUT',
  CHECKEDOUT: 'CHECKED OUT',
  APPROVAL: 'APPROVAL'

};
public TASK_USER_STATUS = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN PROGRESS',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  DELEGATED: 'DELEGATED',
  IN_REQUEST: 'IN_REQUEST',
  IN_APPROVAL: 'IN_APPROVAL',
  APPROVED: 'APPROVED',
  CHANGE_REVIEWER: 'CHANGE_REVIEWER'
};

public IOS_ATTACHMENT_STATUS = {
  DEFAULT: 'DEFAULT',
  QUEUED_FOR_DOWNLOAD: 'QUEUED_FOR_DOWNLOAD',
  DOWNLOADED: 'DOWNLOADED',
  ERROR_IN_DOWNLOAD: 'ERROR_IN_DOWNLOAD',
  SAVED_FOR_UPLOAD: 'SAVED_FOR_UPLOAD',
  UPLOADED: 'UPLOADED',
  ERROR_IN_UPLOAD: 'ERROR_IN_UPLOAD',
  MARKED_FOR_DELETE: 'MARKED_FOR_DELETE'
}

public AND_ATTACHMENT_STATUS = {
  DEFAULT: '0',
  QUEUED_FOR_DOWNLOAD: '1',
  DOWNLOADED: '2',
  ERROR_IN_DOWNLOAD: '3',
  SAVED_FOR_UPLOAD: '4',
  UPLOADED: '5',
  ERROR_IN_UPLOAD: '6',   
  MARKED_FOR_DELETE: '7'
}
public TASK_SOURCE = {
  ADHOC: 'ADHOC',
  ASSIGNED: 'ASSIGNED',
  SHIFT: 'SHIFT',
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  MONTHLY: 'MONTHLY',
  REWORK: 'REWORK'
};

//  *************************** till here *************************** 
  public TASK_MODE = {
    P_MODE_A: 'A',
    P_MODE_M: 'M',
  };

  //Default login Parameters
  //Note: What is defined in config.json will take priority
  public LOGIN_PARAMS = {
    URL: this.DEFAULT_UMP_URL,
    COMPANY: 'UNVIRED',
    USERNAME: '',
    PASSWORD: '',
    APP_NAME: 'PERMIT',
    METADATA_PATH: './assets/js/metadata.json',
  };

  // Object Status for Async call
  public OBJECT_STATUS = {
    GLOBAL: 0,
    ADD: 1,
    MODIFY: 2,
    DELETE: 3,
  };

  // Sync Status for Async call
  public SYNC_STATUS = {
    NONE: 0,
    QUEUED: 1,
    SENT: 2,
    ERROR: 3,
  };

  // Type of devices
  public DEVICE_TYPE = {
    PHONE: 0,
    TABLET: 1,
    DESKTOP: 2,
  };

  // App required permissions
  public REQUIRED_PERMISSION = {
    STORAGE: 'Storage',
    PHONE_STATE: 'Phone State',
    CAMERA: 'Camera',
    LOCATION: 'Location',
    NOTIFICATION: 'Notification',
  };
}
export enum MasterDataType {
  structureTypes = 'structureTypes',
  structureCategories = 'structureCategories',
  structureStatus = 'structureStatus',
  roles = 'roles',
  approvalTypes = 'approvalTypes',
  permitTypes = 'permitTypes',
  skill = 'skill'
}

export enum MasterSelectedMode {
  new = 0,
  edit = 1,
  delete = 2,
}

export enum PermitStatus {
  OPEN = 'OPEN',
  IN_REVIEW = 'IN_REVIEW',
  APPROVED = 'APPROVED',
  ISSUED = 'ISSUED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED',
  REVISE= 'REVISE'
}

export enum PermitStatusProgress {
  OPEN = 0,
  IN_REVIEW = 0.23,
  APPROVED = 0.57,
  ISSUED = 0.71,
  COMPLETED = 1,
  CANCELLED = 1,
}

export enum PermitUserRole {
  REQUEST = 'REQUEST',
  REVIEW = 'REVIEW',
  APPROVE = 'APPROVE',
  ISSUE = 'ISSUE',
  EXTEND = 'EXTEND',
  EXECUTE = 'EXECUTE',
  CLOSE = 'CLOSE',
  CANCEL = 'CANCEL',
  REPORT = 'REPORT',
}
