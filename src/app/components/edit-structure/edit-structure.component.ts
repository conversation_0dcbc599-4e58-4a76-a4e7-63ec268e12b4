import { Component, ElementRef, Input, Ng<PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { LoadingController, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DOCUMENT_ATTACHMENT, DOCUMENT_HEADER, STRUCTURE_CAT_HEADER, STRUCTURE_STATUS_HEADER, STRUCTURE_TYPE_HEADER } from 'src/app/data-models/data_classes';
import { AttachmentsService } from 'src/app/services/attachments.service';
import { DataService } from 'src/app/services/data.service';
import { ImageFullScreenComponent } from '../image-full-screen/image-full-screen.component';
import { LocationPickerComponent } from '../location-picker/location-picker.component';
import * as moment from 'moment';

@Component({
  selector: 'app-edit-structure',
  templateUrl: './edit-structure.component.html',
  styleUrls: ['./edit-structure.component.scss'],
})
export class EditStructureComponent implements OnInit {
  @Input() facilityId: string;
  @Input() divisionId: string;
  @Input() inputStructure: any;
  @ViewChild('structureImage') structureImageInput: ElementRef;

  public structureData: any = {};
  public structureCategories: STRUCTURE_CAT_HEADER[] = [];
  public structureTypes: STRUCTURE_TYPE_HEADER[] = [];
  public structureStatus: STRUCTURE_STATUS_HEADER[] = [];
  public selectedCategoryValue: string = '';
  public selectedTypeValue: string = '';
  public selectedStatusValue: string = '';
  public uploadImageTitle: string = 'Upload Image';
  public uploadedImage: any;
  public isUpdatingImage: boolean = false;
  public canUpdateImage: boolean = false;
  public showImage: boolean = false;
  public documentHeader: DOCUMENT_HEADER;
  public isUpdatingStructure: boolean = false;
  public imageJustUploaded: boolean = false;
  public currentDoc: any;
  public currentAttachment: any;
  public displayError: string = '';
  public title: string = '';
  public progressbar: boolean = false;
  public facilityHasBoundary: boolean = false;
  public facilityBoundary: string = '';

  // Searchable dropdown properties
  public isCategoryDropdownOpen: boolean = false;
  public categorySearchTerm: string = '';
  public filteredStructureCategories: STRUCTURE_CAT_HEADER[] = [];
  public isTypeDropdownOpen: boolean = false;
  public typeSearchTerm: string = '';
  public filteredStructureTypes: STRUCTURE_TYPE_HEADER[] = [];

  constructor(
    public modalCtrl: ModalController,
    public dataService: DataService,
    private loadingController: LoadingController,
    private translate: TranslateService,
    private unviredSDK: UnviredCordovaSDK,
    private attachmentService: AttachmentsService,
    private sanitizer: DomSanitizer,
    private ngZone: NgZone
  ) {
    // Add click listener to close dropdown when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const dropdown = target.closest('.searchable-dropdown');
      if (!dropdown) {
        if (this.isCategoryDropdownOpen) {
          this.isCategoryDropdownOpen = false;
        }
        if (this.isTypeDropdownOpen) {
          this.isTypeDropdownOpen = false;
        }
      }
    });
  }

  async ngOnInit() {
    try {
      // Initialize structure data
      if (this.inputStructure && Object.keys(this.inputStructure).length > 0) {
        this.isUpdatingStructure = true;
        this.structureData = { ...this.inputStructure };
        this.title = 'Update Structure';
      } else {
        this.structureData = {
          FACILITY_ID: this.facilityId,
          DIVISION_ID: this.divisionId,
          TAG: '',
          NAME: ''
        };
        this.title = 'Add Structure';
      }

      // Check if facility has boundary defined
      await this.checkFacilityBoundary();

      // Load structure categories
      let res = await this.dataService.getData('STRUCTURE_CAT_HEADER');
      this.structureCategories = res;
      this.filteredStructureCategories = [...this.structureCategories]; // Initialize filtered list

      // Load structure types
      res = await this.unviredSDK.dbExecuteStatement('SELECT * FROM STRUCTURE_TYPE_HEADER ORDER BY LENGTH(STRUCT_TYPE), STRUCT_TYPE');
      if (res.type == ResultType.success) {
        this.structureTypes = res.data;
        this.filteredStructureTypes = [...this.structureTypes]; // Initialize filtered list
      }

      // Load structure status
      res = await this.dataService.getData('STRUCTURE_STATUS_HEADER');
      this.structureStatus = res;

      // Set default values or load existing values
      if (this.isUpdatingStructure) {
        this.selectedCategoryValue = this.structureData.CATEGORY;
        this.selectedTypeValue = this.structureData.STRUCT_TYPE;
        this.selectedStatusValue = this.structureData.STATUS;

        // Set the display text for the searchable dropdowns
        const selectedCategory = this.structureCategories.find(cat => cat.CATEGORY === this.selectedCategoryValue);
        if (selectedCategory) {
          this.categorySearchTerm = `${selectedCategory.CATEGORY} - ${selectedCategory.DESCRIPTION}`;
        }

        const selectedType = this.structureTypes.find(type => type.STRUCT_TYPE === this.selectedTypeValue);
        if (selectedType) {
          this.typeSearchTerm = `${selectedType.STRUCT_TYPE} - ${selectedType.DESCRIPTION}`;
        }

        await this.getImageThumbnail();
      } else {
        if (this.structureCategories.length > 0) {
          this.selectedCategoryValue = this.structureCategories[0].CATEGORY;
          // Set the display text for the first item
          this.categorySearchTerm = `${this.structureCategories[0].CATEGORY} - ${this.structureCategories[0].DESCRIPTION}`;
        }
        if (this.structureTypes.length > 0) {
          this.selectedTypeValue = this.structureTypes[0].STRUCT_TYPE;
          // Set the display text for the first item
          this.typeSearchTerm = `${this.structureTypes[0].STRUCT_TYPE} - ${this.structureTypes[0].DESCRIPTION}`;
        }
        if (this.structureStatus.length > 0) {
          this.selectedStatusValue = this.structureStatus[0].STATUS;
        }
      }

      this.uploadImageTitle = this.isUpdatingImage ? 'Change Image' : 'Upload Image';
    } catch (error) {
      console.error('Error in ngOnInit:', error);
      this.displayError = 'Error initializing component';
    }
  }

  // Check if the facility has a boundary defined
  async checkFacilityBoundary() {
    try {
      console.log('Checking facility boundary');

      const facilityId = this.structureData.FACILITY_ID || this.facilityId;
      console.log('Facility ID:', facilityId);

      if (!facilityId) {
        console.warn('No facility ID available');
        this.facilityHasBoundary = false;
        this.facilityBoundary = '';
        return;
      }

      const facilityQuery = `SELECT FACILITY_ID, NAME, BOUNDARY FROM FACILITY_HEADER WHERE FACILITY_ID = '${facilityId}'`;
      console.log('Executing query:', facilityQuery);

      const facilityResult = await this.dataService.executeQuery(facilityQuery);
      console.log('Facility query result:', facilityResult);

      if (facilityResult && facilityResult.length > 0) {
        this.facilityBoundary = facilityResult[0].BOUNDARY || '';
        this.facilityHasBoundary = !!this.facilityBoundary;

        console.log(`Facility ${facilityId} (${facilityResult[0].NAME}) has boundary: ${this.facilityHasBoundary}`);

        if (this.facilityBoundary) {
          console.log('Boundary data:', this.facilityBoundary);
        } else {
          console.warn('No boundary defined for this facility');
        }
      } else {
        console.warn(`No facility found with ID: ${facilityId}`);
        this.facilityHasBoundary = false;
        this.facilityBoundary = '';
      }
    } catch (error) {
      console.error('Error checking facility boundary:', error);
      this.facilityHasBoundary = false;
      this.facilityBoundary = '';
    }
  }

  // Trigger file input click
  triggerFileInput() {
    this.structureImageInput.nativeElement.click();
  }

  // Get image thumbnail
  async getImageThumbnail() {
    try {
      let res = await this.dataService.executeQuery(
        `SELECT * FROM STRUCTURE_DOC WHERE TAG='${this.structureData.TAG}' AND DIVISION_ID='${this.structureData.DIVISION_ID}' AND FACILITY_ID='${this.structureData.FACILITY_ID}'`
      );
      this.documentHeader = res;

      this.ngZone.run(() => {
        if (res?.length > 0) {
          if (res[0].THUMBNAIL !== ('' || null)) {
            this.uploadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
              `data:image/png;base64,${res[0].THUMBNAIL}`
            );
            this.canUpdateImage = true;
            this.isUpdatingImage = true;
            this.showImage = true;
          }
        } else {
          this.isUpdatingImage = false;
          this.canUpdateImage = false;
          this.showImage = false;
        }
      });

      return this.documentHeader;
    } catch (error) {
      console.error('Error in getImageThumbnail:', error);
      return [];
    }
  }

  // Upload files
  async uploadFiles(files) {
    if (files.length === 0) return;

    var mimeType = files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    var reader = new FileReader();
    reader.readAsDataURL(files[0]);
    reader.onload = async (_event) => {
      this.uploadedImage = reader.result;
      await this.uploadImage(files);
    };
  }

  // Upload image
  async uploadImage(files) {
    try {
      this.progressbar = true;
      await this.displayPleaseWaitLoader(this.translate.instant('Please wait, uploading image...'));

      this.attachmentService.uploadFileToServer(files[0]).then(
        async (result) => {
          let documetAttachmentHeader = this.createDocumentAttachmentHeader(
            result.attachmentId,
            files[0]
          );
          let documetHeader = await this.createDocumentHeader(
            this.canUpdateImage
              ? this.documentHeader[0].DOC_ID
              : result.attachmentId,
            files[0],
            this.canUpdateImage ? 'M' : 'A'
          );

          let documetHeaderInput = {
            DOCUMENT: [
              {
                DOCUMENT_HEADER: documetHeader,
                DOCUMENT_ATTACHMENT: [documetAttachmentHeader],
              },
            ],
          };

          let res: any = await this.attachmentService.uploadAttachment(
            documetHeaderInput
          );

          this.loadingController.dismiss();
          this.progressbar = false;

          if (res) {
            if (res.type == ResultType.success) {
              await this.unviredSDK.dbSaveWebData();
              if (res.data.InfoMessage === undefined) {
                this.showImage = true;
                this.currentDoc = res.data.DOCUMENT[0].DOCUMENT_HEADER;
                this.currentAttachment = res.data.DOCUMENT[0].DOCUMENT_ATTACHMENT;
                this.imageJustUploaded = true;
                this.canUpdateImage = true;
                this.uploadImageTitle = 'Change Image';
              } else if (res.data.InfoMessage.length > 0) {
                this.displayError = this.translate.instant(res.message.trim());
              }
            } else if (res.type == ResultType.error) {
              if (res.code && res.code === 401) {
                await this.dataService.logoutToInitialPage();
              } else if (res.data?.InfoMessage?.length > 0) {
                this.displayError = res.message.trim();
              }
            }
          } else {
            this.displayError = this.translate.instant(
              this.isUpdatingStructure
                ? 'Error Updating Structure'
                : 'Error Adding Structure'
            );
          }
        },
        (error) => {
          this.loadingController.dismiss();
          this.progressbar = false;
          this.displayError = this.translate.instant(
            'Attachment upload failed, Please try again'
          );
        }
      );
    } catch (error) {
      this.loadingController.dismiss();
      this.progressbar = false;
      console.error('Error in uploadImage:', error);
      this.displayError = 'Error uploading image';
    }
  }

  // Create document header
  async createDocumentHeader(
    attachmentUid: string,
    file: any,
    mode: string
  ): Promise<any> {
    var documentHeader = <DOCUMENT_HEADER>{};
    documentHeader.DOC_ID = attachmentUid;
    documentHeader.FILE_NAME = file.name;
    documentHeader.TITLE = '';
    documentHeader.CREATED_BY = '';
    documentHeader.CREATED_ON = moment().unix();
    documentHeader.P_MODE = mode;

    // Fetch user id from user role table.
    let userResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_ROLE`
    );
    if (userResult.type == ResultType.success) {
      if (userResult.data && userResult.data.length > 0) {
        documentHeader.CREATED_BY = userResult.data[0].USER_ID;
      }
    }
    return documentHeader;
  }

  // Create document attachment header
  createDocumentAttachmentHeader(attachmentUid: string, file: any): any {
    var newAttachment = <DOCUMENT_ATTACHMENT>{};
    newAttachment.UID = attachmentUid;
    newAttachment.FILE_NAME = file.name;
    newAttachment.MIME_TYPE = file.type;
    newAttachment.ATTACHMENT_STATUS = 'UPLOADED';
    return newAttachment;
  }

  // Save structure
  async saveStructure() {
    try {
      this.progressbar = true;
      this.displayError = '';

      await this.displayPleaseWaitLoader(
        this.translate.instant(this.isUpdatingStructure ? 'Please wait, updating structure...' : 'Please wait, adding structure...')
      );

      let structureHeader = {
        LID: this.unviredSDK.guid().replace(/-/g, ''),
        FACILITY_ID: this.structureData.FACILITY_ID,
        DIVISION_ID: this.structureData.DIVISION_ID,
        TAG: this.structureData.TAG,
        NAME: this.structureData.NAME,
        CATEGORY: this.selectedCategoryValue,
        STRUCT_TYPE: this.selectedTypeValue,
        STATUS: this.selectedStatusValue,
        LATITUDE: this.structureData.LATITUDE || null,
        LONGITUDE: this.structureData.LONGITUDE || null,
        P_MODE: this.isUpdatingStructure ? 'M' : 'A',
        OBJECT_STATUS: this.isUpdatingStructure ? 2 : 1,
      };

      await this.unviredSDK.dbInsertOrUpdate(
        'STRUCTURE_HEADER',
        structureHeader,
        true
      );

      if (this.imageJustUploaded) {
        let structureDoc = {
          FID: structureHeader.LID,
          FACILITY_ID: this.structureData.FACILITY_ID,
          DIVISION_ID: this.structureData.DIVISION_ID,
          TAG: this.structureData.TAG,
          DOC_ID: this.currentDoc.DOC_ID,
          DOC_TYPE: 'image',
          THUMBNAIL: '',
          P_MODE: this.isUpdatingImage ? 'M' : 'A',
          OBJECT_STATUS: this.isUpdatingImage ? 2 : 1,
        };

        await this.unviredSDK.dbInsertOrUpdate(
          'STRUCTURE_DOC',
          structureDoc,
          false
        );
      }

      let result: any = await this.dataService.modifyStructure({
        STRUCTURE_HEADER: structureHeader,
      });

      this.loadingController.dismiss();
      this.progressbar = false;

      if (result) {
        if (result.type == ResultType.success) {
          await this.unviredSDK.dbSaveWebData();

          if (result.data.InfoMessage === undefined) {
            // Success - close the modal
            this.modalCtrl.dismiss({
              'success': true,
              'structure': structureHeader
            });
          } else if (result.data.InfoMessage.length > 0) {
            this.displayError = this.translate.instant(result.message.trim());
          }
        } else if (result.type == ResultType.error) {
          if (result.code && result.code === 401) {
            await this.dataService.logoutToInitialPage();
          } else if (result.data?.InfoMessage?.length > 0) {
            this.displayError = result.message.trim();
          }
        }
      } else {
        this.displayError = this.translate.instant(
          this.isUpdatingStructure
            ? 'Error Updating Structure'
            : 'Error Adding Structure'
        );
      }
    } catch (error) {
      this.loadingController.dismiss();
      this.progressbar = false;
      console.error('Error in saveStructure:', error);
      this.displayError = 'Error saving structure';
    }
  }

  // Display image in full screen
  async imageInFullscreen() {
    try {
      this.progressbar = true;
      await this.displayPleaseWaitLoader(this.translate.instant('Please wait...'));

      let documentHeader = await this.getImageThumbnail();
      const documentData = {
        DOCUMENT: [
          {
            DOCUMENT_HEADER: {
              DOC_ID: documentHeader[0].DOC_ID,
              NAME: '',
              DESCR: '',
              CREATED_BY: '',
              CREATED_ON: '',
              P_MODE: '',
            },
          },
        ],
      };

      let response = await this.dataService.getDocument(documentData);

      this.loadingController.dismiss();
      this.progressbar = false;

      if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
        let responseData = await this.attachmentService.downloadAndWriteAttachmentToFile(
          response.DOCUMENT_ATTACHMENT[0].UID,
          response.DOCUMENT_ATTACHMENT[0].FILE_NAME
        );

        const modal = await this.modalCtrl.create({
          component: ImageFullScreenComponent,
          componentProps: {
            imageData: responseData,
            documentName: this.structureData.TAG,
          },
        });

        await modal.present();
      }
    } catch (error) {
      this.loadingController.dismiss();
      this.progressbar = false;
      console.error('Error in imageInFullscreen:', error);
      this.displayError = 'Error displaying image';
    }
  }

  // Display Loading dialog
  async displayPleaseWaitLoader(message: string = 'Please wait...') {
    const loading = await this.loadingController.create({
      message: message,
      backdropDismiss: false,
    });
    await loading.present();
  }

  // Searchable dropdown methods for Category
  toggleCategoryDropdown() {
    this.isCategoryDropdownOpen = !this.isCategoryDropdownOpen;
    if (this.isCategoryDropdownOpen) {
      this.categorySearchTerm = '';
      this.filteredStructureCategories = [...this.structureCategories];
      // Close type dropdown if open
      this.isTypeDropdownOpen = false;
      // Focus the search input after a short delay
      setTimeout(() => {
        const searchInput = document.querySelector('.dropdown-search input') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }

  openCategoryDropdown() {
    if (!this.isCategoryDropdownOpen) {
      this.isCategoryDropdownOpen = true;
      this.categorySearchTerm = '';
      this.filteredStructureCategories = [...this.structureCategories];
      // Close type dropdown if open
      this.isTypeDropdownOpen = false;
      // Focus the search input after a short delay
      setTimeout(() => {
        const searchInput = document.querySelector('.dropdown-search input') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }

  filterStructureCategories() {
    if (!this.categorySearchTerm || this.categorySearchTerm.trim() === '') {
      this.filteredStructureCategories = [...this.structureCategories];
    } else {
      const searchTerm = this.categorySearchTerm.toLowerCase();
      this.filteredStructureCategories = this.structureCategories.filter(category =>
        category.CATEGORY.toLowerCase().includes(searchTerm) ||
        category.DESCRIPTION.toLowerCase().includes(searchTerm)
      );
    }
  }

  selectStructureCategory(category: STRUCTURE_CAT_HEADER) {
    this.selectedCategoryValue = category.CATEGORY;
    this.categorySearchTerm = `${category.CATEGORY} - ${category.DESCRIPTION}`;
    this.isCategoryDropdownOpen = false;
  }

  // Searchable dropdown methods for Type
  toggleTypeDropdown() {
    this.isTypeDropdownOpen = !this.isTypeDropdownOpen;
    if (this.isTypeDropdownOpen) {
      this.typeSearchTerm = '';
      this.filteredStructureTypes = [...this.structureTypes];
      // Close category dropdown if open
      this.isCategoryDropdownOpen = false;
      // Focus the search input after a short delay
      setTimeout(() => {
        const searchInput = document.querySelector('.dropdown-search input') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }

  openTypeDropdown() {
    if (!this.isTypeDropdownOpen) {
      this.isTypeDropdownOpen = true;
      this.typeSearchTerm = '';
      this.filteredStructureTypes = [...this.structureTypes];
      // Close category dropdown if open
      this.isCategoryDropdownOpen = false;
      // Focus the search input after a short delay
      setTimeout(() => {
        const searchInput = document.querySelector('.dropdown-search input') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }

  filterStructureTypes() {
    if (!this.typeSearchTerm || this.typeSearchTerm.trim() === '') {
      this.filteredStructureTypes = [...this.structureTypes];
    } else {
      const searchTerm = this.typeSearchTerm.toLowerCase();
      this.filteredStructureTypes = this.structureTypes.filter(type =>
        type.STRUCT_TYPE.toLowerCase().includes(searchTerm) ||
        type.DESCRIPTION.toLowerCase().includes(searchTerm)
      );
    }
  }

  selectStructureType(type: STRUCTURE_TYPE_HEADER) {
    this.selectedTypeValue = type.STRUCT_TYPE;
    this.typeSearchTerm = `${type.STRUCT_TYPE} - ${type.DESCRIPTION}`;
    this.isTypeDropdownOpen = false;
  }

  // Keyboard event handlers
  onCategorySearchKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      this.isCategoryDropdownOpen = false;
      event.preventDefault();
    } else if (event.key === 'Enter' && this.filteredStructureCategories.length > 0) {
      this.selectStructureCategory(this.filteredStructureCategories[0]);
      event.preventDefault();
    }
  }

  onTypeSearchKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      this.isTypeDropdownOpen = false;
      event.preventDefault();
    } else if (event.key === 'Enter' && this.filteredStructureTypes.length > 0) {
      this.selectStructureType(this.filteredStructureTypes[0]);
      event.preventDefault();
    }
  }

  // Close modal
  closeModal() {
    this.modalCtrl.dismiss();
  }

  // Open location picker modal
  async openLocationPicker() {
    try {
      console.log('Opening location picker');

      // Check if facility has boundary defined
      if (!this.facilityHasBoundary) {
        this.displayError = 'Cannot select location: Facility boundary is not defined';
        console.warn('Facility boundary not defined');
        return;
      }

      console.log('Facility boundary:', this.facilityBoundary);
      console.log('Initial coordinates:', {
        lat: this.structureData.LATITUDE,
        lng: this.structureData.LONGITUDE
      });

      // Ensure we have valid initial coordinates
      const initialLatitude = this.structureData.LATITUDE || null;
      const initialLongitude = this.structureData.LONGITUDE || null;

      const modal = await this.modalCtrl.create({
        component: LocationPickerComponent,
        cssClass: 'map-modal',
        componentProps: {
          initialLatitude: initialLatitude,
          initialLongitude: initialLongitude,
          facilityBoundary: this.facilityBoundary
        }
      });

      await modal.present();

      const { data } = await modal.onDidDismiss();
      if (data) {
        console.log('Location selected:', data);
        this.structureData.LATITUDE = data.latitude;
        this.structureData.LONGITUDE = data.longitude;
      } else {
        console.log('No location selected (modal dismissed)');
      }
    } catch (error) {
      console.error('Error opening location picker:', error);
      this.displayError = 'Error opening location picker';
    }
  }
}
