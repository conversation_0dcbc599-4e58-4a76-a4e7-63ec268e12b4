<div class="modal-header">
  <div class="title">{{ title }}</div>
  <button class="close-button" (click)="closeModal()">✕</button>
</div>
<ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>

<div class="form-container">
    <div class="form-row">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Enter Structure Tag" [disabled]="isUpdatingStructure" [(ngModel)]="structureData.TAG"
          required="true" labelPlacement="stacked" fill="outline">
          <div slot="label">Structure Tag <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="pricetag-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Enter Structure Description" [(ngModel)]="structureData.NAME" maxlength="100"
          required="true" labelPlacement="stacked" fill="outline">
          <div slot="label">Structure Description <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <div class="searchable-dropdown-container">
          <label class="dropdown-label">Structure Category <ion-text color="danger">*</ion-text></label>
          <div class="searchable-dropdown" [class.dropdown-open]="isCategoryDropdownOpen">
            <div class="dropdown-input-wrapper" (click)="toggleCategoryDropdown()">
              <ion-icon name="list-outline" class="dropdown-icon"></ion-icon>
              <input
                type="text"
                class="dropdown-input"
                placeholder="Select Structure Category"
                [(ngModel)]="categorySearchTerm"
                (input)="filterStructureCategories()"
                (click)="$event.stopPropagation(); openCategoryDropdown()"
                readonly="{{!isCategoryDropdownOpen}}"
              />
              <ion-icon name="chevron-down-outline" class="dropdown-arrow" [class.rotated]="isCategoryDropdownOpen"></ion-icon>
            </div>
            <div class="dropdown-options" *ngIf="isCategoryDropdownOpen">
              <div class="dropdown-search" *ngIf="isCategoryDropdownOpen">
                <ion-icon name="search-outline" class="search-icon"></ion-icon>
                <input
                  type="text"
                  placeholder="Search structure categories..."
                  [(ngModel)]="categorySearchTerm"
                  (input)="filterStructureCategories()"
                  (keydown)="onCategorySearchKeydown($event)"
                  #categorySearchInput
                />
              </div>
              <div class="dropdown-option"
                   *ngFor="let category of filteredStructureCategories"
                   (click)="selectStructureCategory(category)"
                   [class.selected]="selectedCategoryValue === category.CATEGORY">
                {{category.CATEGORY}} - {{category.DESCRIPTION}}
              </div>
              <div class="no-options" *ngIf="filteredStructureCategories.length === 0">
                No structure categories found
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <div class="searchable-dropdown-container">
          <label class="dropdown-label">Structure Type <ion-text color="danger">*</ion-text></label>
          <div class="searchable-dropdown" [class.dropdown-open]="isTypeDropdownOpen">
            <div class="dropdown-input-wrapper" (click)="toggleTypeDropdown()">
              <ion-icon name="construct-outline" class="dropdown-icon"></ion-icon>
              <input
                type="text"
                class="dropdown-input"
                placeholder="Select Structure Type"
                [(ngModel)]="typeSearchTerm"
                (input)="filterStructureTypes()"
                (click)="$event.stopPropagation(); openTypeDropdown()"
                readonly="{{!isTypeDropdownOpen}}"
              />
              <ion-icon name="chevron-down-outline" class="dropdown-arrow" [class.rotated]="isTypeDropdownOpen"></ion-icon>
            </div>
            <div class="dropdown-options" *ngIf="isTypeDropdownOpen">
              <div class="dropdown-search" *ngIf="isTypeDropdownOpen">
                <ion-icon name="search-outline" class="search-icon"></ion-icon>
                <input
                  type="text"
                  placeholder="Search structure types..."
                  [(ngModel)]="typeSearchTerm"
                  (input)="filterStructureTypes()"
                  (keydown)="onTypeSearchKeydown($event)"
                  #typeSearchInput
                />
              </div>
              <div class="dropdown-option"
                   *ngFor="let type of filteredStructureTypes"
                   (click)="selectStructureType(type)"
                   [class.selected]="selectedTypeValue === type.STRUCT_TYPE">
                {{type.STRUCT_TYPE}} - {{type.DESCRIPTION}}
              </div>
              <div class="no-options" *ngIf="filteredStructureTypes.length === 0">
                No structure types found
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
          placeholder="Select Structure Status" fill="outline" labelPlacement="stacked"
          [(ngModel)]="selectedStatusValue">
          <div slot="label">Structure Status <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="stats-chart-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
          <ion-select-option *ngFor="let status of structureStatus" [value]="status.STATUS">
            {{status.STATUS}} - {{status.DESCRIPTION}}
          </ion-select-option>
        </ion-select>
      </div>
    </div>

    <!-- Location Fields -->
    <div class="form-row location-fields">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Latitude" [(ngModel)]="structureData.LATITUDE"
          type="number" step="0.000001" labelPlacement="stacked" fill="outline"
          [disabled]="!facilityHasBoundary">
          <div slot="label">Latitude</div>
          <ion-icon slot="start" name="location-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Longitude" [(ngModel)]="structureData.LONGITUDE"
          type="number" step="0.000001" labelPlacement="stacked" fill="outline"
          [disabled]="!facilityHasBoundary">
          <div slot="label">Longitude</div>
          <ion-icon slot="start" name="navigate-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <!-- Location Warning Message -->
    <div class="form-row" *ngIf="!facilityHasBoundary">
      <div class="form-group location-warning">
        <ion-text color="warning">
          <ion-icon name="warning-outline"></ion-icon>
          Location selection is disabled because the facility boundary is not defined.
        </ion-text>
      </div>
    </div>

    <!-- Map Button -->
    <div class="form-row">
      <div class="form-group">
        <ion-button class="map-button" (click)="openLocationPicker()" [disabled]="!facilityHasBoundary">
          <ion-icon name="map-outline" slot="start"></ion-icon>
          Select Location on Map
        </ion-button>
      </div>
    </div>

    <p class="error">{{displayError | translate}}</p>
  </div>

<div class="footer-buttons">
  <button class="cancel-button" (click)="closeModal()">Cancel</button>
  <button class="save-button"
    [disabled]="!structureData.TAG || !structureData.NAME || !selectedCategoryValue || !selectedTypeValue || !selectedStatusValue"
    (click)="saveStructure()">Save</button>
</div>
