# Searchable Dropdown Implementation

## Overview
This implementation adds searchable dropdown functionality to the Structure Type and Structure Category fields in the edit-structure component, similar to PrimeNG's dropdown with filter functionality.

## Features
- **Search functionality**: Users can type to filter options
- **Keyboard navigation**: 
  - `Escape` key to close dropdown
  - `Enter` key to select first filtered option
- **Click outside to close**: Dropdown closes when clicking outside
- **Visual feedback**: Selected option is highlighted
- **Responsive design**: Works on both desktop and mobile

## Implementation Details

### HTML Structure
```html
<div class="searchable-dropdown-container">
  <label class="dropdown-label">Field Name <ion-text color="danger">*</ion-text></label>
  <div class="searchable-dropdown" [class.dropdown-open]="isDropdownOpen">
    <div class="dropdown-input-wrapper" (click)="toggleDropdown()">
      <ion-icon name="icon-name" class="dropdown-icon"></ion-icon>
      <input 
        type="text" 
        class="dropdown-input"
        placeholder="Select option"
        [(ngModel)]="searchTerm"
        (input)="filterOptions()"
        readonly="{{!isDropdownOpen}}"
      />
      <ion-icon name="chevron-down-outline" class="dropdown-arrow" [class.rotated]="isDropdownOpen"></ion-icon>
    </div>
    <div class="dropdown-options" *ngIf="isDropdownOpen">
      <div class="dropdown-search">
        <ion-icon name="search-outline" class="search-icon"></ion-icon>
        <input 
          type="text" 
          placeholder="Search options..."
          [(ngModel)]="searchTerm"
          (input)="filterOptions()"
          (keydown)="onSearchKeydown($event)"
        />
      </div>
      <div class="dropdown-option" 
           *ngFor="let option of filteredOptions" 
           (click)="selectOption(option)"
           [class.selected]="selectedValue === option.value">
        {{option.display}}
      </div>
      <div class="no-options" *ngIf="filteredOptions.length === 0">
        No options found
      </div>
    </div>
  </div>
</div>
```

### TypeScript Properties
```typescript
public isDropdownOpen: boolean = false;
public searchTerm: string = '';
public filteredOptions: any[] = [];
public selectedValue: string = '';
```

### TypeScript Methods
```typescript
toggleDropdown() {
  this.isDropdownOpen = !this.isDropdownOpen;
  if (this.isDropdownOpen) {
    this.searchTerm = '';
    this.filteredOptions = [...this.allOptions];
  }
}

filterOptions() {
  if (!this.searchTerm || this.searchTerm.trim() === '') {
    this.filteredOptions = [...this.allOptions];
  } else {
    const searchTerm = this.searchTerm.toLowerCase();
    this.filteredOptions = this.allOptions.filter(option => 
      option.value.toLowerCase().includes(searchTerm) ||
      option.description.toLowerCase().includes(searchTerm)
    );
  }
}

selectOption(option: any) {
  this.selectedValue = option.value;
  this.searchTerm = `${option.value} - ${option.description}`;
  this.isDropdownOpen = false;
}
```

### CSS Classes
- `.searchable-dropdown-container`: Main container
- `.dropdown-label`: Floating label
- `.searchable-dropdown`: Dropdown wrapper
- `.dropdown-input-wrapper`: Input container with icons
- `.dropdown-options`: Options container
- `.dropdown-search`: Search input container
- `.dropdown-option`: Individual option
- `.selected`: Selected option styling
- `.no-options`: No results message

## Usage in Other Components
To use this searchable dropdown in other components:

1. Copy the HTML structure
2. Add the TypeScript properties and methods
3. Include the CSS styles
4. Initialize the filtered options array
5. Handle the selection logic

## Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Ionic framework compatible

## Performance Considerations
- Filtering is done client-side for small datasets
- For large datasets (>1000 items), consider server-side filtering
- Virtual scrolling can be added for very large lists

## Accessibility
- Keyboard navigation support
- ARIA labels can be added for screen readers
- Focus management for better UX
