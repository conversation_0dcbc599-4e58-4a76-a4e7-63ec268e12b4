/* Modal header styling */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #3f51b5;
  color: white;
  padding: 15px 20px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.modal-header .title {
  font-size: 18px;
  font-weight: 500;
}

.modal-header .close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

/* Footer buttons styling */
.footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
  background-color: white;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
}

.cancel-button, .save-button {
  border: none;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  margin-left: 10px;
  min-width: 80px;
}

.cancel-button {
  background-color: #f44336;
}

.save-button {
  background-color: #4caf50;
}

.save-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

/* Form layout */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-width: 100%;
  padding: 15px;
  padding-bottom: 60px; /* Add padding at the bottom to ensure content doesn't get hidden behind footer */
  margin-bottom: 0;
  overflow-y: auto;
  max-height: calc(100vh - 120px); /* Account for header and footer */
}

.form-row {
  display: flex;
  gap: 20px;
  width: 100%;
  margin-bottom: 5px;
}

.form-group {
  flex: 1;
  min-width: 0;
}

/* Validation styling */
.validation-error {
  color: var(--ion-color-danger);
  font-size: 12px;
  margin-left: 16px;
  margin-bottom: 8px;
}

:host ::ng-deep ion-input.ion-invalid {
  --border-color: var(--ion-color-danger) !important;
}

:host ::ng-deep ion-input.ion-valid {
  --border-color: var(--ion-color-success) !important;
}

/* Floating label styling */
ion-item {
  --border-color: #ced4da;
  --border-radius: 4px;
  --background: transparent;
  margin-bottom: 0px;
}

ion-label {
  font-weight: 500;
  color: #00629b !important;
}

.error {
  text-align: center;
  color: #d63232;
  margin-top: 10px;
}

/* Image styling */
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
}

.structure-image {
  width: 300px;
  height: 200px;
  cursor: pointer;
  margin: 0;
  overflow: hidden;
}

.structure-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-button {
  margin-top: 10px;
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
}

/* Location fields styling */
.location-fields {
  display: flex;
  gap: 10px;
}

.location-fields .form-group {
  flex: 1;
}

.map-button {
  margin-top: 10px;
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  --background: #24348B;
  width: 100%;
}

/* Map modal styling */
.map-modal {
  --height: 80%;
  --width: 90%;
  --border-radius: 10px;
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4);
}

.map-container {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.map-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.map-instructions {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 10px;
  border-radius: 8px;
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Location warning styling */
.location-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 3px solid var(--ion-color-warning);
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
}

.location-warning ion-icon {
  vertical-align: middle;
  margin-right: 8px;
  color: var(--ion-color-warning);
}

/* Searchable dropdown styling */
.searchable-dropdown-container {
  position: relative;
  width: 100%;
}

.dropdown-label {
  position: absolute;
  top: -8px;
  left: 16px;
  background: white;
  padding: 0 4px;
  font-size: 12px;
  color: #666;
  z-index: 1;
  font-weight: 500;
}

.searchable-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid var(--ion-color-step-300, #b3b3b3);
  border-radius: 4px;
  background: white;
  min-height: 38px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.dropdown-input-wrapper:hover {
  border-color: var(--ion-color-primary);
}

.dropdown-open .dropdown-input-wrapper {
  border-color: var(--ion-color-primary);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.dropdown-icon {
  margin-left: 16px;
  margin-right: 8px;
  color: #00629b;
  font-size: 18px;
}

.dropdown-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 8px;
  font-size: 14px;
  background: transparent;
  cursor: pointer;
}

.dropdown-input:focus {
  cursor: text;
}

.dropdown-arrow {
  margin-right: 16px;
  color: #666;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--ion-color-primary);
  border-top: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dropdown-search {
  position: sticky;
  top: 0;
  background: white;
  border-bottom: 1px solid #eee;
  padding: 8px;
  display: flex;
  align-items: center;
}

.search-icon {
  color: #666;
  margin-right: 8px;
  font-size: 16px;
}

.dropdown-search input {
  flex: 1;
  border: none;
  outline: none;
  padding: 8px;
  font-size: 14px;
  background: #f8f9fa;
  border-radius: 4px;
}

.dropdown-option {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.dropdown-option:hover {
  background-color: #f8f9fa;
}

.dropdown-option.selected {
  background-color: #d8dbee;
  color: var(--ion-color-primary);
  font-weight: 500;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.no-options {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
}
