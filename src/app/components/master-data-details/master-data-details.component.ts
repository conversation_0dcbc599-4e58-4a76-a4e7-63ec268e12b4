import { Component, OnInit } from '@angular/core';
import { ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Loading<PERSON>ontroller, ModalController } from '@ionic/angular';
import {
  APPROVAL_TYPE_HEADER,
  PERMIT_TYPE_APPROVAL,
  PERMIT_TYPE_HEADER,
  ROLE_HEADER,
  STRUCTURE_CAT_HEADER,
  STRUCTURE_STATUS_HEADER,
  STRUCTURE_TYPE_HEADER,
  SKILL_HEADER,
} from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import {
  MasterDataType,
  MasterSelectedMode,
} from 'src/app/shared/app-constants';

@Component({
  selector: 'app-master-data-details',
  templateUrl: './master-data-details.component.html',
  styleUrls: ['./master-data-details.component.scss'],
})
export class MasterDataDetailsComponent implements OnInit {
  public selectedTab: string;
  public data: any;
  public mode: number;
  public title: string;
  public structureType: any;
  public structureCategorie: any;
  public structureStatus: any;
  public rca: any;
  public priorities: any;
  public inspection: any;
  public role: any;
  public structureTypeErrorMsg: string;
  public structureCategorieErrorMsg: string;
  public structureStatusErrorMsg: string;
  public rcaErrorMsg: string;
  public inspectionTypeErrorMsg: string;
  public prioritiesErrorMsg: string;
  public rolesErrorMsg: string;
  public frequencyList: number[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  public approvalTypeErrorMsg: string;
  public approvalType: any;
  public permitTypeErrorMsg: string;
  public permitType: any;
  public skill: any;

  public skillTypeErrorMsg: string
  public formsList: any[] = [];
  // Font Awesome icons for permit types
  public iconsList = [
    { name: '', displayName: 'Select an icon', class: 'fas fa-question-circle' },
    { name: 'fire', displayName: 'Hot Work', class: 'fas fa-fire' },
    { name: 'snowflake', displayName: 'Cold Work', class: 'fas fa-snowflake' },
    { name: 'arrow-up', displayName: 'Height Work', class: 'fas fa-arrow-up' },
    { name: 'door-open', displayName: 'Confined Spaces', class: 'fas fa-door-open' },
    { name: 'shovel', displayName: 'Excavation', class: 'fas fa-shovel' },
    { name: 'bolt', displayName: 'Electrical', class: 'fas fa-bolt' },
    { name: 'exclamation-triangle', displayName: 'Special Hazard', class: 'fas fa-exclamation-triangle' },
    { name: 'hard-hat', displayName: 'Construction', class: 'fas fa-hard-hat' },
    { name: 'radiation', displayName: 'Radiation', class: 'fas fa-radiation' },
    { name: 'biohazard', displayName: 'Biohazard', class: 'fas fa-biohazard' },
    { name: 'tools', displayName: 'Maintenance', class: 'fas fa-tools' },
    { name: 'toolbox', displayName: 'Mechanical', class: 'fas fa-toolbox' },
    { name: 'question-circle', displayName: 'Others', class: 'fas fa-question-circle' }
  ];
  public scopeList = [
    {
      key: 'Internal',
      value: 'I'
    },
    {
      key: 'External',
      value: 'E'
    },
    {
      key: 'Both',
      value: 'B'
    }
  ]
  formName: string = '';
  public permitTypesList: any[] = [];
  public defaultSelectedTypes: any[] = [];
  public approvalsList:any[] = [];
  public selectedApprovals: string[] = []; // Selected approval types for multi-select
  public skillsList: any[] = [];
  public progressbar: boolean = false;
  constructor(
    public modalController: ModalController,
    private dataService: DataService,
    private loadingController: LoadingController
  ) { }

  async ngOnInit() {
    switch (this.selectedTab) {
      case MasterDataType.structureTypes:
        this.structureTypeErrorMsg = '';
        if (this.mode == MasterSelectedMode.edit) {
          this.title = 'Edit Structure Type';
          this.structureType = {
            id: this.data.STRUCT_TYPE,
            desc: this.data.DESCRIPTION,
          };
        } else {
          this.structureType = { id: '', desc: '' };
          this.title = 'Add Structure Type';
        }
        break;
      case MasterDataType.structureCategories:
        this.structureCategorieErrorMsg = '';
        if (this.mode == MasterSelectedMode.edit) {
          this.title = 'Edit Structure Category';
          this.structureCategorie = {
            id: this.data.CATEGORY,
            desc: this.data.DESCRIPTION,
          };
        } else {
          this.structureCategorie = { id: '', desc: '' };
          this.title = 'Add Structure Category';
        }
        break;
      case MasterDataType.structureStatus:
        this.structureStatusErrorMsg = '';
        if (this.mode == MasterSelectedMode.edit) {
          this.title = 'Edit Structure Status';
          this.structureStatus = {
            id: this.data.STATUS,
            desc: this.data.DESCRIPTION,
            isActive: this.data.IS_ACTIVE,
          };
        } else {
          this.structureStatus = { id: '', desc: '', isActive: '' };
          this.title = 'Add Structure Status';
        }
        break;
      case MasterDataType.roles:
        this.rolesErrorMsg = '';
        if (this.mode == MasterSelectedMode.new) {
          this.title = 'Add Role';
          this.role = {
            ROLE_NAME: '',
            IS_INTERNAL: false,
            CONFIGURATION: false,
            USER_MGMT: false,
            AGENT_MGMT: false,
            PROC_MGMT: false,
            REQUEST: false,
            APPROVE: false,
            ISSUE: false,
            EXTEND: false,
            EXECUTE: false,
            CLOSE: false,
            CANCEL: false,
            REPORT: false,
          };
        }
        break;
      case MasterDataType.approvalTypes:
        this.approvalTypeErrorMsg = '';
        if (this.mode == MasterSelectedMode.edit) {
          this.title = 'Edit Approval Type';
          this.approvalType = {
            type: this.data.APPR_TYPE,
            desc: this.data.DESCRIPTION,
            scope: this.data.SCOPE,
          };
        } else {
          this.approvalType = { type: '', desc: '', scope: '' };
          this.title = 'Add Approval Type';
        }
        break;

      case MasterDataType.permitTypes:
        this.permitTypeErrorMsg = '';
        if (this.mode == MasterSelectedMode.edit) {
          this.title = 'Edit Permit Type';

          // Ensure icon and color have default values if not present
          const icon = this.data.ICON || 'fire';
          let color = this.data.COLOR;

          // Ensure color is a number
          if (color === undefined || color === null) {
            color = 0; // Default color (blue)
          } else if (typeof color === 'string') {
            color = parseInt(color, 10) || 0;
          }

          this.permitType = {
            type: this.data.PERMIT_TYPE,
            desc: this.data.DESCRIPTION,
            prefix: this.data.PREFIX,
            form_id: this.data.FORM_ID,
            icon: icon,
            color: color
          };

          console.log('Initialized permit type for editing:', this.permitType);
        } else {
          // Use the app theme color (Ionic blue) as default
          const themeColor = 3880255; // #3880ff in decimal

          this.permitType = {
            type: '',
            desc: '',
            prefix: '',
            form_id: '',
            icon: '', // Empty by default to prompt user selection
            color: themeColor
          };
          this.title = 'Add Permit Type';

          console.log('Initialized new permit type:', this.permitType);
        }
        break;

        case MasterDataType.skill:
          this.skillTypeErrorMsg = '';
        if (this.mode == MasterSelectedMode.edit) {
          this.title = 'Edit Skill Type';
          console.log("the data in edit is " , this.data)
          this.skill = {
            type: this.data.SKILL_TYPE,
            desc: this.data.DESCRIPTION,
          };
        } else {
          this.skill = { type: '', desc: '', scope: '' };
          this.title = 'Add Skill Type';
        }
        break;


    }
    await this.getSkillTypes();
    await this.getApprovalTypes();
    await this.getPermitTypes();
    await this.getForms();

  }

  closeModal() {
    this.modalController.dismiss();
  }

  // Display Loading dialog.
  async displayPleaseWaitLoader() {
    const loading = await this.loadingController.create({
      message: 'Please wait' + '...',
      backdropDismiss: false,
    });
    await loading.present();
  }

  async save() {
    let value = this.selectedTab;
    switch (value) {
      case MasterDataType.structureTypes:
        this.structureTypeErrorMsg = '';
        await this.displayPleaseWaitLoader();

        let structureType = new STRUCTURE_TYPE_HEADER();
        structureType.DESCRIPTION = this.structureType.desc;
        structureType.STRUCT_TYPE = this.structureType.id;

        let structureTypeInput = {
          STRUCTURE_TYPE: [
            {
              STRUCTURE_TYPE_HEADER: structureType,
            },
          ],
        };

        if (this.mode == MasterSelectedMode.new) {
          structureType.P_MODE = 'A';
        } else if (this.mode == MasterSelectedMode.edit) {
          structureType.P_MODE = 'M';
        }

        let result: any = await this.dataService.modifyStructureType(
          structureTypeInput
        );
        let resultInfoMsg = this.dataService.handleInfoMessage(result);
        if (result.type == ResultType.success) {
          if (resultInfoMsg && resultInfoMsg?.length > 0) {
            this.structureTypeErrorMsg = resultInfoMsg;
          } else {
            this.loadingController.dismiss();
            await this.modalController.dismiss({ status: true });
          }
        } else {
          this.loadingController.dismiss();
          if (
            result.message &&
            result.message.length > 0
          ) {
            this.structureTypeErrorMsg = result.message;
          } else if (
            result.error &&
            result.error.length > 0
          ) {
            this.structureTypeErrorMsg = result.error;
          } else {
            this.structureTypeErrorMsg = 'Error occured while updating structure type, Please try again';
          }
        }
        break;

      case MasterDataType.structureCategories:
        this.structureCategorieErrorMsg = '';
        await this.displayPleaseWaitLoader();

        let structureCategories = new STRUCTURE_CAT_HEADER();
        structureCategories.DESCRIPTION = this.structureCategorie.desc;
        structureCategories.CATEGORY = this.structureCategorie.id;

        let structureCategoriesInput = {
          STRUCTURE_CAT: [
            {
              STRUCTURE_CAT_HEADER: structureCategories,
            },
          ],
        };

        if (this.mode == MasterSelectedMode.new) {
          structureCategories.P_MODE = 'A';
        } else if (this.mode == MasterSelectedMode.edit) {
          structureCategories.P_MODE = 'M';
        }

        let structureCategoriesResult: any =
          await this.dataService.modifyStructureCategories(
            structureCategoriesInput
          );

        let structureCategoriesInfoMsg = this.dataService.handleInfoMessage(structureCategoriesResult);
        if (structureCategoriesResult.type == ResultType.success) {
          if (structureCategoriesInfoMsg && structureCategoriesInfoMsg?.length > 0) {
            this.structureCategorieErrorMsg = structureCategoriesInfoMsg;
          } else {
            this.loadingController.dismiss();
            await this.modalController.dismiss({ status: true });
          }
        } else {
          this.loadingController.dismiss();
          if (
            structureCategoriesResult.message &&
            structureCategoriesResult.message.length > 0
          ) {
            this.structureCategorieErrorMsg = structureCategoriesResult.message;
          } else if (
            structureCategoriesResult.error &&
            structureCategoriesResult.error.length > 0
          ) {
            this.structureCategorieErrorMsg = structureCategoriesResult.error;
          } else {
            this.structureCategorieErrorMsg = 'Error occured while updating structure category, Please try again';
          }
        }
        break;

      case MasterDataType.structureStatus:
        this.structureStatusErrorMsg = '';
        await this.displayPleaseWaitLoader();

        let structureStatus = new STRUCTURE_STATUS_HEADER();
        structureStatus.DESCRIPTION = this.structureStatus.desc;
        structureStatus.STATUS = this.structureStatus.id;
        if (this.structureStatus.isActive == true) {
          structureStatus.IS_ACTIVE = 'X';
        } else {
          structureStatus.IS_ACTIVE = '';
        }

        let structureStatusInput = {
          STRUCTURE_STATUS: [
            {
              STRUCTURE_STATUS_HEADER: structureStatus,
            },
          ],
        };

        if (this.mode == MasterSelectedMode.new) {
          structureStatus.P_MODE = 'A';
        } else if (this.mode == MasterSelectedMode.edit) {
          structureStatus.P_MODE = 'M';
        }

        let structureStatusResult: any =
          await this.dataService.modifyStructureStatus(structureStatusInput);

        let structureStatuInfoMsg = this.dataService.handleInfoMessage(structureStatusResult);
        if (structureStatusResult.type == ResultType.success) {
          if (structureStatuInfoMsg && structureStatuInfoMsg?.length > 0) {
            this.structureStatusErrorMsg = structureStatuInfoMsg;
          } else {
            this.loadingController.dismiss();
            await this.modalController.dismiss({ status: true });
          }
        } else {
          this.loadingController.dismiss();
          if (
            structureStatusResult.message &&
            structureStatusResult.message.length > 0
          ) {
            this.structureStatusErrorMsg = structureStatusResult.message;
          } else if (
            structureStatusResult.error &&
            structureStatusResult.error.length > 0
          ) {
            this.structureStatusErrorMsg = structureStatusResult.error;
          } else {
            this.structureStatusErrorMsg = 'Error occured while updating structure status, Please try again';
          }
        }
        break;

      case MasterDataType.roles:
        this.rolesErrorMsg = '';
        await this.displayPleaseWaitLoader();

        let roles = new ROLE_HEADER();
        roles.ROLE_NAME = this.role.ROLE_NAME;
        roles.IS_INTERNAL = this.role.IS_INTERNAL == true ? 'true' : 'false';
        roles.CONFIGURATION =
          this.role.CONFIGURATION == true ? 'true' : 'false';
        roles.USER_MGMT = this.role.USER_MGMT == true ? 'true' : 'false';
        roles.AGENT_MGMT = this.role.AGENT_MGMT == true ? 'true' : 'false';
        roles.PROC_MGMT = this.role.PROC_MGMT == true ? 'true' : 'false';
        roles.REQUEST = this.role.REQUEST == true ? 'true' : 'false';
        roles.APPROVE = this.role.APPROVE == true ? 'true' : 'false';
        roles.ISSUE = this.role.ISSUE == true ? 'true' : 'false';
        roles.EXTEND = this.role.EXTEND == true ? 'true' : 'false';
        roles.EXECUTE = this.role.EXECUTE == true ? 'true' : 'false';
        roles.CLOSE = this.role.CLOSE == true ? 'true' : 'false';
        roles.CANCEL = this.role.CANCEL == true ? 'true' : 'false';
        roles.REPORT = this.role.REPORT == true ? 'true' : 'false';
        roles.P_MODE = 'A';

        let rolesInput = {
          ROLE: [
            {
              ROLE_HEADER: roles,
            },
          ],
        };

        let rolesResult: any = await this.dataService.modifyRoles(rolesInput);
        let rolesInfoMsg = this.dataService.handleInfoMessage(rolesResult);
        if (rolesResult.type == ResultType.success) {
          if (rolesInfoMsg && rolesInfoMsg?.length > 0) {
            this.rolesErrorMsg = rolesInfoMsg;
          } else {
            this.loadingController.dismiss();
            await this.modalController.dismiss({ status: true });
          }
        } else {
          this.loadingController.dismiss();
          if (
            rolesResult.message &&
            rolesResult.message.length > 0
          ) {
            this.rolesErrorMsg = rolesResult.message;
          } else if (
            rolesResult.error &&
            rolesResult.error.length > 0
          ) {
            this.rolesErrorMsg = rolesResult.error;
          } else {
            this.rolesErrorMsg = 'Error occured while updating role, Please try again';
          }
        }
        break;

      case MasterDataType.approvalTypes:
        this.approvalTypeErrorMsg = '';
        await this.displayPleaseWaitLoader();

        let approvertype = new APPROVAL_TYPE_HEADER();
        approvertype.DESCRIPTION = this.approvalType.desc;
        approvertype.APPR_TYPE = this.approvalType.type;
        approvertype.SCOPE = this.approvalType.scope;

        let approverTypeInput = {
          APPROVAL_TYPE: [
            {
              APPROVAL_TYPE_HEADER: approvertype,
            },
          ],
        };

        if (this.mode == MasterSelectedMode.new) {
          approvertype.P_MODE = 'A';
        } else if (this.mode == MasterSelectedMode.edit) {
          approvertype.P_MODE = 'M';
        }

        let approverTypeResult: any = await this.dataService.modifyApprovalType(
          approverTypeInput
        );
        let approverTypeInfoMsg = this.dataService.handleInfoMessage(approverTypeResult);
        if (approverTypeResult.type == ResultType.success) {
          if (approverTypeInfoMsg && approverTypeInfoMsg?.length > 0) {
            this.approvalTypeErrorMsg = approverTypeInfoMsg;
          } else {
            this.loadingController.dismiss();
            await this.modalController.dismiss({ status: true });
          }
        } else {
          this.loadingController.dismiss();
          if (
            approverTypeResult.message &&
            approverTypeResult.message.length > 0
          ) {
            this.approvalTypeErrorMsg = approverTypeResult.message;
          } else if (
            approverTypeResult.error &&
            approverTypeResult.error.length > 0
          ) {
            this.approvalTypeErrorMsg = approverTypeResult.error?.error ? approverTypeResult.error?.error : approverTypeResult.error;
          } else {
            this.approvalTypeErrorMsg = 'Error occured while updating approval type, Please try again';
          }
        }
        break;

      case MasterDataType.permitTypes:
        let temp = [];
        let tempList: any = [];

        // Use the selectedApprovals array to get the selected approval types
        let selApproval = [];
        if (this.selectedApprovals && this.selectedApprovals.length > 0) {
          this.selectedApprovals.forEach(selectedApprType => {
            const approval = this.approvalsList.find(a => a.APPR_TYPE === selectedApprType);
            if (approval) {
              selApproval.push(approval);
            }
          });
        }
        if (selApproval?.length > 0) {
          let included = this.defaultSelectedTypes.filter(
            (item) => !selApproval.includes(item)
          );
          let notIncluded = selApproval.filter(
            (item) => !this.defaultSelectedTypes.includes(item)
          );

          if (included?.length > 0) {
            for (let f = 0; f < included.length; f++) {
              let userApprovalHeader = {} as PERMIT_TYPE_APPROVAL;
              userApprovalHeader.P_MODE = 'D';
              userApprovalHeader.PERMIT_TYPE = included[f].PERMIT_TYPE
              userApprovalHeader.APPR_TYPE = included[f].APPR_TYPE
              temp.push(userApprovalHeader)
            }
          }

          if (notIncluded?.length > 0) {
            for (let f = 0; f < notIncluded.length; f++) {
              let userApprovalHeader = {} as PERMIT_TYPE_APPROVAL;
              userApprovalHeader.P_MODE = 'A';
              userApprovalHeader.PERMIT_TYPE = notIncluded[f].PERMIT_TYPE
              userApprovalHeader.APPR_TYPE = notIncluded[f].APPR_TYPE
              temp.push(userApprovalHeader)
            }
          }
          if (temp.length > 0) {

            temp.forEach(element => {
              let obj = {
                "APPR_TYPE": element.APPR_TYPE,
                "PERMIT_TYPE": this.permitType.type,
                "P_MODE": element.P_MODE
              }
              tempList.push(obj)
            });
          }
        }

        this.permitTypeErrorMsg = '';
        await this.displayPleaseWaitLoader();
        if(this.permitType.form_id != '' ){
          let res = await this.dataService.executeQuery(`select FORM_TITLE as name from FORM_HEADER where form_id = '${this.permitType.form_id}'`);
          if (res?.length > 0) {
            this.formName = res[0].name;
          }
        }else{
          this.permitType.form_id = '';
          this.formName = '';
        }

        let permittype = new PERMIT_TYPE_HEADER();
        permittype.DESCRIPTION = this.permitType.desc;
        permittype.PERMIT_TYPE = this.permitType.type;
        permittype.PREFIX = this.permitType.prefix;
        permittype.FORM_ID = this.permitType.form_id;
        permittype.FORM_NAME = this.formName;

        // Ensure icon and color are properly set
        if (!this.permitType.icon) {
          // If no icon is selected, use the question-circle (Others) icon
          permittype.ICON = 'question-circle';
        } else {
          permittype.ICON = this.permitType.icon;
        }

        // Use the app theme color (Ionic blue) as default
        const themeColor = 3880255; // #3880ff in decimal

        // Handle color - ensure it's a number
        if (this.permitType.color !== undefined && this.permitType.color !== null) {
          if (typeof this.permitType.color === 'string') {
            permittype.COLOR = parseInt(this.permitType.color as string, 10) || themeColor; // Default to theme color if parsing fails
          } else {
            permittype.COLOR = this.permitType.color;
          }
        } else {
          permittype.COLOR = themeColor; // Default to app theme color
        }

        // Log the values to ensure they're being set correctly
        console.log('Saving permit type with icon:', permittype.ICON);
        console.log('Saving permit type with color:', permittype.COLOR);
        let permitTypeInput: any;
        if (tempList.length > 0) {
          permitTypeInput = {
            "PERMIT_TYPE": [
              {
                PERMIT_TYPE_HEADER: permittype,
                "PERMIT_TYPE_APPROVAL": tempList
              }
            ]
          }
        } else {
          permitTypeInput = {
            PERMIT_TYPE: [
              {
                PERMIT_TYPE_HEADER: permittype,
                PERMIT_TYPE_APPROVAL: [] // Ensure this is included even when empty
              }
            ]
          };
        }

        // Log the full input object to verify structure
        console.log('Permit type input object:', JSON.stringify(permitTypeInput));

        if (this.mode == MasterSelectedMode.new) {
          permittype.P_MODE = 'A';
        } else if (this.mode == MasterSelectedMode.edit) {
          permittype.P_MODE = 'M';
        }

        let permitTypeResult: any = await this.dataService.modifyPermitType(
          permitTypeInput
        );
        console.log('Permit type save result:', permitTypeResult);

        // If successful, verify the data was saved correctly by retrieving it
        if (permitTypeResult.type === ResultType.success) {
          try {
            const query = `SELECT * FROM PERMIT_TYPE_HEADER WHERE PERMIT_TYPE = '${permittype.PERMIT_TYPE}'`;
            const result = await this.dataService.executeQuery(query);
            console.log('Saved permit type data:', result);
          } catch (error) {
            console.error('Error retrieving saved permit type:', error);
          }
        }
        let permitTypeInfoMsg = this.dataService.handleInfoMessage(permitTypeResult);
        if (permitTypeResult.type == ResultType.success) {
          if (permitTypeInfoMsg && permitTypeInfoMsg?.length > 0) {
            this.permitTypeErrorMsg = permitTypeInfoMsg;
          } else {
            this.loadingController.dismiss();
            await this.modalController.dismiss({ status: true });
          }
        } else {
          this.loadingController.dismiss();
          if (
            permitTypeResult.message &&
            permitTypeResult.message.length > 0
          ) {
            this.permitTypeErrorMsg = permitTypeResult.message;
          } else if (
            permitTypeResult.error &&
            permitTypeResult.error.length > 0
          ) {
            this.permitTypeErrorMsg = permitTypeResult.error;
          } else {
            this.permitTypeErrorMsg = 'Error occured while updating permit type, Please try again';
          }
        }
        break;

      case MasterDataType.skill:
        this.skillTypeErrorMsg = '';
        await this.displayPleaseWaitLoader();
        let skillType = new SKILL_HEADER();
        skillType.DESCRIPTION = this.skill.desc;
        skillType.SKILL_TYPE = this.skill.type;


        let skillTypeInput = {
          SKILL: [
            {
              SKILL_HEADER: skillType,
            },
          ],
        };

        if (this.mode == MasterSelectedMode.new) {
          skillType.P_MODE = 'A';
        } else if (this.mode == MasterSelectedMode.edit) {
          skillType.P_MODE = 'M';
        }

        let skillTypeResult: any = await this.dataService.modifySkillType(
          skillTypeInput
        );
        let skillTypeInfoMsg = this.dataService.handleInfoMessage(skillTypeResult);
        if (skillTypeResult.type == ResultType.success) {
          if (skillTypeInfoMsg && skillTypeInfoMsg?.length > 0) {
            this.skillTypeErrorMsg = skillTypeInfoMsg;
          } else {
            this.loadingController.dismiss();
            await this.modalController.dismiss({ status: true });
          }
        } else {
          this.loadingController.dismiss();
          if (
            skillTypeResult.message &&
            skillTypeResult.message.length > 0
          ) {
            this.skillTypeErrorMsg = skillTypeResult.message;
          } else if (
            skillTypeResult.error &&
            skillTypeResult.error.length > 0
          ) {
            this.skillTypeErrorMsg = skillTypeResult.error?.error ? skillTypeResult.error?.error : skillTypeResult.error;
          } else {
            this.skillTypeErrorMsg = 'Error occured while updating skill type, Please try again';
          }
        }
        break;

      }
  }

  async getForms() {
    this.progressbar = true;
    this.formsList = await this.dataService.getData('FORM_HEADER');
    if (this.formsList.length == 0) {
      await this.dataService.getFormTemplates();
      this.formsList = await this.dataService.getData('FORM_HEADER');
    }
    this.progressbar = false;
  }

  async getApprovalTypes() {
    this.progressbar = true;
    this.approvalsList = await this.dataService.getData('APPROVAL_TYPE_HEADER', null, 'APPR_TYPE ASC');
    this.progressbar = false;
  }

  async getSkillTypes() {

    this.progressbar = true;
    this.skillsList = await this.dataService.getData('SKILL_HEADER', null, '');
    this.progressbar = false;
  }



  async getPermitTypes() {
    this.progressbar = true;
    await this.dataService.getCustomization();
    this.permitTypesList = await this.dataService.getData('PERMIT_TYPE_APPROVAL',  `PERMIT_TYPE = '${this.data?.PERMIT_TYPE}'`);
    if (this.permitTypesList?.length > 0) {
      // Initialize selectedApprovals array
      this.selectedApprovals = [];

      for (let ff = 0; ff < this.permitTypesList?.length; ff++) {
        for (let ff1 = 0; ff1 < this.approvalsList.length; ff1++) {
          if (this.permitTypesList[ff].APPR_TYPE == this.approvalsList[ff1].APPR_TYPE) {
            this.approvalsList[ff1].isChecked = true;
            this.defaultSelectedTypes.push(this.approvalsList[ff1]);
            // Add to selectedApprovals array for multi-select
            this.selectedApprovals.push(this.approvalsList[ff1].APPR_TYPE);
          }
        }
      }
    }
    this.progressbar = false;
  }

  // Update approval checks based on selectedApprovals
  updateApprovalChecks() {
    // Reset all approval checks
    this.approvalsList.forEach(approval => {
      approval.isChecked = false;
    });

    // Set isChecked based on selectedApprovals
    if (this.selectedApprovals && this.selectedApprovals.length > 0) {
      this.selectedApprovals.forEach(selectedApprType => {
        const approval = this.approvalsList.find(a => a.APPR_TYPE === selectedApprType);
        if (approval) {
          approval.isChecked = true;
        }
      });
    }
  }

  // Remove approval from selectedApprovals
  removeApproval(approvalType: string) {
    // Remove the approval from the selectedApprovals array
    this.selectedApprovals = this.selectedApprovals.filter(a => a !== approvalType);

    // Update the approval checks
    this.updateApprovalChecks();
  }


  preventSpaces(event: KeyboardEvent) {
    if (event.key === ' ') {
      event.preventDefault();
      return false;
    }
    return true;
  }

  // Handle color change event from the color picker
  onColorChange(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.value) {
      const colorNumber = this.getColorNumber(inputElement.value);
      this.permitType.color = colorNumber;
      console.log('Color changed to:', inputElement.value, 'Number value:', colorNumber);
    }
  }

  // Convert color number to hex string for the color picker
  getColorHex(colorNumber: number): string {
    // App theme color (Ionic blue)
    const themeColorHex = '#3880ff';

    // Default to app theme color
    if (colorNumber === undefined || colorNumber === null || colorNumber === 0) {
      return themeColorHex;
    }

    // Convert number to hex string and ensure it has 6 digits
    let hexString = colorNumber.toString(16);
    hexString = hexString.padStart(6, '0');

    return '#' + hexString;
  }

  // Convert hex string from color picker to number for storage
  getColorNumber(hexColor: string): number {
    if (!hexColor || !hexColor.startsWith('#')) return 0;

    // Remove the # and convert to a number
    const hexValue = hexColor.substring(1);
    return parseInt(hexValue, 16);
  }

  // Get display name for an icon by its name
  getIconDisplayName(iconName: string): string {
    if (!iconName) {
      return 'Select an icon';
    }
    const icon = this.iconsList.find(i => i.name === iconName);
    return icon ? icon.displayName : 'Select an icon';
  }

  // Get CSS class for an icon by its name
  getIconClass(iconName: string): string {
    if (!iconName) {
      return 'fas fa-question-circle';
    }
    const icon = this.iconsList.find(i => i.name === iconName);
    return icon ? icon.class : 'fas fa-question-circle';
  }
}


