<div>
<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>{{title}}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()"><ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>
</ion-header>

  <!--#region Structure Type-->
  <div *ngIf="selectedTab == 'structureTypes'" style="padding: 20px;">
    <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
      placeholder="Enter Code" [disabled]="mode == 1" maxlength="10" [(ngModel)]="structureType.id"
      labelPlacement="stacked" fill="outline">
      <div slot="label">Code <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="code-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-input>

    <ion-textarea style="--padding-start: 16px !important;min-height: 100px;margin-bottom: 20px;"
      placeholder="Enter Description" maxlength="100" [(ngModel)]="structureType.desc"
      labelPlacement="stacked" fill="outline" rows="3" spellcheck="true">
      <div slot="label">Description <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-textarea>
  </div>



  <!--#endregion-->

  <!--#region Structure Categories-->
  <div *ngIf="selectedTab == 'structureCategories'" style="padding: 20px;">
    <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
      placeholder="Enter Code" [disabled]="mode == 1" maxlength="5" [(ngModel)]="structureCategorie.id"
      labelPlacement="stacked" fill="outline">
      <div slot="label">Code <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="code-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-input>

    <ion-textarea style="--padding-start: 16px !important;min-height: 100px;margin-bottom: 20px;"
      placeholder="Enter Description" maxlength="40" [(ngModel)]="structureCategorie.desc"
      labelPlacement="stacked" fill="outline" rows="3" spellcheck="true">
      <div slot="label">Description <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-textarea>



  </div>
  <!--#endregion-->

  <!--#region Structure Status-->
  <div *ngIf="selectedTab == 'structureStatus'" style="padding: 20px;">
    <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
      placeholder="Enter Code" [disabled]="mode == 1" maxlength="5" [(ngModel)]="structureStatus.id"
      labelPlacement="stacked" fill="outline">
      <div slot="label">Code <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="code-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-input>

    <ion-textarea style="--padding-start: 16px !important;min-height: 100px;margin-bottom: 20px;"
      placeholder="Enter Description" maxlength="40" [(ngModel)]="structureStatus.desc"
      labelPlacement="stacked" fill="outline" rows="3" spellcheck="true">
      <div slot="label">Description <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-textarea>


  </div>
  <!--#endregion-->

  <!--#region Roles-->
  <div *ngIf="selectedTab == 'roles'" style="padding: 20px;">
    <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
      placeholder="{{ 'Enter Role Name' | translate }}" [disabled]="mode == 1" maxlength="20"
      [(ngModel)]="role.ROLE_NAME" (keyup.enter)="(!role.ROLE_NAME) ? '' : save()"
      labelPlacement="stacked" fill="outline">
      <div slot="label">{{ "Role Name" | translate }} <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="person-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-input>

    <ion-card style="margin-top: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
      <ion-grid>
        <ion-row>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Is Internal" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.IS_INTERNAL" [checked]="role.IS_INTERNAL"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Configuration" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.CONFIGURATION" [checked]="role.CONFIGURATION"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "User Management" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.USER_MGMT" [checked]="role.USER_MGMT"></ion-toggle>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Agent Management" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.AGENT_MGMT" [checked]="role.AGENT_MGMT"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Procedure Management" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.PROC_MGMT" [checked]="role.PROC_MGMT"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Facility Management" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.FACILITY_MGMT" [checked]="role.FACILITY_MGMT"></ion-toggle>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Review" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.REVIEW" [checked]="role.REVIEW"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Repair" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.REPAIR" [checked]="role.REPAIR"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Verify" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.VERIFY" [checked]="role.VERIFY"></ion-toggle>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Report" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.REPORT" [checked]="role.REPORT"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item lines="none" class="toggle-item">
              <ion-label class="toggle-label">{{ "Inspect" | translate }}</ion-label>
              <ion-toggle [(ngModel)]="role.INSPECT" [checked]="role.INSPECT"></ion-toggle>
            </ion-item>
          </ion-col>
          <ion-col></ion-col>
        </ion-row>
      </ion-grid>
    </ion-card>



  </div>
  <!--#endregion-->


  <!--#region Approval Type-->
  <div *ngIf="selectedTab == 'approvalTypes'" style="padding: 20px;">
    <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
      placeholder="Enter Type" [disabled]="mode == 1" maxlength="10" [(ngModel)]="approvalType.type"
      labelPlacement="stacked" fill="outline">
      <div slot="label">Type <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="document-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-input>

    <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
      [(ngModel)]="approvalType.scope" labelPlacement="stacked" fill="outline" interface="popover">
      <div slot="label">Scope <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="options-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
      <ion-select-option *ngFor="let scope of scopeList" [value]="scope.value">{{scope.key}}</ion-select-option>
    </ion-select>

    <ion-textarea style="--padding-start: 16px !important;min-height: 100px;margin-bottom: 20px;"
      placeholder="Enter Description" [(ngModel)]="approvalType.desc"
      labelPlacement="stacked" fill="outline" rows="3" spellcheck="true">
      <div slot="label">Description <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-textarea>
  </div>



  <!--#endregion-->

  <!--#region Permit Type-->
  <div *ngIf="selectedTab == 'permitTypes'" style="padding: 20px;">

    <ion-grid>
    <ion-row>
      <ion-col size="6">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
          placeholder="Enter Type" [disabled]="mode == 1" maxlength="10" [(ngModel)]="permitType.type"
          labelPlacement="stacked" fill="outline" (keypress)="preventSpaces($event)">
          <div slot="label">Type <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="document-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>

        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
          placeholder="Enter Prefix" [disabled]="mode == 1" maxlength="3" [(ngModel)]="permitType.prefix"
          labelPlacement="stacked" fill="outline">
          <div slot="label">Prefix <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>

        <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
          [(ngModel)]="permitType.form_id" labelPlacement="stacked" fill="outline" interface="popover">
          <div slot="label">Form</div>
          <ion-select-option *ngFor="let form of formsList" [value]="form.FORM_ID">{{form.FORM_TITLE || form.FORM_NAME}}</ion-select-option>
        </ion-select>

        <ion-textarea style="--padding-start: 16px !important;min-height: 100px;margin-bottom: 20px;"
          placeholder="Enter Description" maxlength="40" [(ngModel)]="permitType.desc"
          labelPlacement="stacked" fill="outline" rows="3" spellcheck="true">
          <div slot="label">Description <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-textarea>

        <!-- Preview of selected icon and color -->
        <div style="margin-bottom: 20px;">
          <ion-card style="margin: 0; padding: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <ion-label style="font-size: 12px; display: block; margin-bottom: 8px; color: #666;">Preview</ion-label>
            <div style="display: flex; align-items: center;">
              <i [class]="getIconClass(permitType.icon)" [style.color]="getColorHex(permitType.color)" style="font-size: 20px; margin-right: 12px;"></i>
              <span style="font-weight: 500;">{{permitType.desc || 'Permit Type Description'}}</span>
            </div>
          </ion-card>
        </div>
      </ion-col>
      <ion-col size="6">
        <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
          placeholder="Select Approval Types" labelPlacement="stacked" fill="outline" multiple="true"
          [(ngModel)]="selectedApprovals" (ionChange)="updateApprovalChecks()">
          <div slot="label">Select Approval Types</div>
          <ion-icon slot="start" name="checkmark-circle-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
          <ion-select-option *ngFor="let approval of approvalsList" [value]="approval.APPR_TYPE">{{approval.APPR_TYPE}}</ion-select-option>
        </ion-select>

        <!-- Display selected approvals as badges -->
        <div class="approval-badges" *ngIf="selectedApprovals && selectedApprovals.length > 0">
          <ion-badge *ngFor="let approval of selectedApprovals" color="primary" class="approval-badge">
            {{ approval }}
            <ion-icon name="close-circle" (click)="removeApproval(approval)"></ion-icon>
          </ion-badge>
        </div>

        <!-- Icon and Color Selection in a row -->
        <div style="display: flex; gap: 10px; margin-bottom: 20px; align-items: flex-end;">
          <!-- Icon Selection Dropdown -->
          <ion-select style="--padding-start: 16px !important;min-height: 38px; flex: 3;"
            [(ngModel)]="permitType.icon" labelPlacement="stacked" fill="outline" interface="popover"
            (ionChange)="permitType.icon = $event.detail.value">
            <div slot="label">Icon <ion-text color="danger">*</ion-text></div>
            <div slot="value">
              <span style="display: flex; align-items: center;">
                <i [class]="getIconClass(permitType.icon)" style="margin-right: 8px; color: #00629b;"></i>
                {{getIconDisplayName(permitType.icon)}}
              </span>
            </div>
            <ion-icon slot="start" name="apps-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            <ion-select-option *ngFor="let icon of iconsList" [value]="icon.name">
              <span style="display: flex; align-items: center;">
                <i [class]="icon.class" style="margin-right: 8px;"></i>
                {{icon.displayName}}
              </span>
            </ion-select-option>
          </ion-select>

          <!-- Color Picker with badge -->
          <div style="flex: 2;">
            <div style="display: flex; flex-direction: column;">
              <ion-label style="font-size: 12px; margin-bottom: 5px; padding-left: 5px;">
                Color <ion-text color="danger">*</ion-text>
              </ion-label>
              <div style="display: flex; align-items: center; height: 38px;">
                <div [style.background-color]="getColorHex(permitType.color)"
                     style="display: flex; align-items: center; justify-content: center; padding: 0 15px; border-radius: 4px; height: 100%; cursor: pointer;"
                     (click)="colorInput.click()">
                  <span style="color: white; font-size: 14px; font-weight: 500;">Choose a color</span>
                </div>
                <input #colorInput type="color" [value]="getColorHex(permitType.color)"
                      (change)="onColorChange($event)"
                      (input)="onColorChange($event)"
                      style="width: 0; height: 0; opacity: 0; position: absolute;">
              </div>
            </div>
          </div>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
  </div>



  <!--#endregion-->



  <!-- Skill set -->

  <div *ngIf="selectedTab == 'skill'" style="padding: 20px;">
    <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 20px;"
      placeholder="Enter Skill Type" [disabled]="mode == 1" maxlength="5" [(ngModel)]="skill.type"
      labelPlacement="stacked" fill="outline">
      <div slot="label">Skill Type <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="construct-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-input>

    <ion-textarea style="--padding-start: 16px !important;min-height: 100px;margin-bottom: 20px;"
      placeholder="Enter Description" maxlength="100" [(ngModel)]="skill.desc"
      labelPlacement="stacked" fill="outline" rows="3" spellcheck="true">
      <div slot="label">Description <ion-text color="danger">*</ion-text></div>
      <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
    </ion-textarea>
  </div>

  <div style="text-align:center; color:indianred;padding: 10px 20px 60px 20px;">
    <p *ngIf="selectedTab == 'structureTypes'">{{structureTypeErrorMsg}}</p>
    <p *ngIf="selectedTab == 'structureCategories'">{{structureCategorieErrorMsg}}</p>
    <p *ngIf="selectedTab == 'structureStatus'">{{structureStatusErrorMsg}}</p>
    <p *ngIf="selectedTab == 'roles'">{{rolesErrorMsg}}</p>
    <p *ngIf="selectedTab == 'approvalTypes'">{{approvalTypeErrorMsg}}</p>
    <p *ngIf="selectedTab == 'permitTypes'">{{permitTypeErrorMsg}}</p>
    <p *ngIf="selectedTab == 'skill'">{{skillTypeErrorMsg}}</p>
  </div>
</div>
<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="closeModal()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md"
      [disabled]="(selectedTab == 'structureTypes' && (!structureType.id || !structureType.desc)) ||
                 (selectedTab == 'structureCategories' && (!structureCategorie.id || !structureCategorie.desc)) ||
                 (selectedTab == 'structureStatus' && (!structureStatus.id || !structureStatus.desc)) ||
                 (selectedTab == 'roles' && !role.ROLE_NAME) ||
                 (selectedTab == 'approvalTypes' && (!approvalType.type || !approvalType.scope || !approvalType.desc)) ||
                 (selectedTab == 'permitTypes' && (!permitType.type || !permitType.prefix || !permitType.desc || !permitType.icon)) ||
                 (selectedTab == 'skill' && (!skill.type || !skill.desc))"
      (click)="save()">Save</ion-button>
  </ion-toolbar>
</ion-footer>