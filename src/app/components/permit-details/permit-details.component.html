<div>
<ion-header style="box-shadow: none !important;border-bottom: 1px solid gainsboro;">
  <ion-toolbar color="primary">
    <ion-title style="font-weight: 600;font-size: large;padding-inline: 0px;">{{permit.PERMIT_NO}}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()">
        <ion-icon slot="icon-only" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-toolbar>
    <ion-segment [(ngModel)]="segmentValue" style="margin: 0px 25px 0px 20px; width: 200px; justify-content: flex-start;" mode="md">
      <ion-segment-button value="details" style="min-width: 80px; width: 100px;">
        <ion-label style="font-weight: 600;">Permit</ion-label>
      </ion-segment-button>
      <ion-segment-button value="form" (click)="showForm()" style="min-width: 80px; width: 100px;">
        <ion-label style="font-weight: 600;">Form</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>

<div style="--background: #f6f7f8;--offset-top: 0px !important; margin-top: 0px;">
  <div [ngSwitch]="segmentValue" style="margin-top: 0px;">
    <div *ngSwitchCase="'details'" style="margin-top: 0px;"
      [ngClass]="{'isDisable': isReport || permit.STATUS == 'CLOSED' || permit.STATUS == 'CANCELLED'}">

      <!-- Scrollable content area for Details tab -->
      <div [ngClass]="isMobile ? 'content-area-mobile' : 'content-area-desktop'" style="height: calc(100vh - 168px); overflow-y: auto; background: #f5f5f5; margin-top: 0px;">

        <!-- Shimmer Loading for Details -->
        <ng-container *ngIf="isDetailsLoading">
          <ion-grid>
            <ion-row style="margin-top: 0px; font-size: 14px;">
              <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="6" sizeXl="6">

                <!-- General Card Shimmer -->
                <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
                  <!-- Section Header Shimmer -->
                  <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                    <div class="shimmer-section-title shimmer" style="width: 80px; height: 16px; border-radius: 4px;"></div>
                    <div class="shimmer-edit-icon shimmer" style="width: 18px; height: 18px; border-radius: 50%;"></div>
                  </div>
                  <ion-card-content style="padding: 16px;">
                    <ion-grid style="padding: 0;">
                      <ion-row>
                        <ion-col size="12">
                          <!-- Status and Permit Type Row -->
                          <ion-row style="margin-bottom: 12px;">
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 40px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-status-chip shimmer" style="width: 60px; height: 24px; border-radius: 12px;"></div>
                            </ion-col>
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 70px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer" style="width: 90%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                          </ion-row>

                          <!-- Facility and Division Row -->
                          <ion-row style="margin-bottom: 12px;">
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 45px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer" style="width: 85%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 50px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer" style="width: 75%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                          </ion-row>

                          <!-- Tag and Job Number Row -->
                          <ion-row style="margin-bottom: 12px;">
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 25px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div style="display: flex; align-items: center; gap: 8px;">
                                <div class="shimmer-field-value shimmer" style="width: 60%; height: 14px; border-radius: 2px;"></div>
                                <div class="shimmer-icon shimmer" style="width: 16px; height: 16px; border-radius: 2px;"></div>
                                <div class="shimmer-icon shimmer" style="width: 16px; height: 16px; border-radius: 2px;"></div>
                              </div>
                            </ion-col>
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 75px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer" style="width: 70%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                          </ion-row>

                          <!-- Description Row -->
                          <ion-row>
                            <ion-col size="12">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 70px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer" style="width: 95%; height: 14px; border-radius: 2px; margin-bottom: 4px;"></div>
                              <div class="shimmer-field-value shimmer" style="width: 60%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                          </ion-row>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                  </ion-card-content>
                </ion-card>

                <!-- Agents Card Shimmer -->
                <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
                  <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                    <div class="shimmer-section-title shimmer" style="width: 60px; height: 16px; border-radius: 4px;"></div>
                    <div class="shimmer-edit-icon shimmer" style="width: 18px; height: 18px; border-radius: 50%;"></div>
                  </div>
                  <ion-card-content style="padding: 16px;">
                    <ion-grid style="padding: 0;">
                      <ion-row>
                        <ion-col size="12">
                          <ion-row>
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 50px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer" style="width: 80%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer" style="width: 55px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer" style="width: 75%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                          </ion-row>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                  </ion-card-content>
                </ion-card>

                <!-- Validity Card Shimmer -->
                <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
                  <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                    <div class="shimmer-section-title shimmer-wave" style="width: 55px; height: 16px; border-radius: 4px;"></div>
                    <div class="shimmer-edit-icon shimmer-wave" style="width: 18px; height: 18px; border-radius: 50%;"></div>
                  </div>
                  <ion-card-content style="padding: 16px;">
                    <ion-grid style="padding: 0;">
                      <ion-row>
                        <ion-col size="12">
                          <ion-row>
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer-wave" style="width: 30px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer-wave" style="width: 85%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                            <ion-col size="6">
                              <div style="margin-bottom: 4px;">
                                <div class="shimmer-field-label shimmer-wave" style="width: 25px; height: 12px; border-radius: 2px;"></div>
                              </div>
                              <div class="shimmer-field-value shimmer-wave" style="width: 85%; height: 14px; border-radius: 2px;"></div>
                            </ion-col>
                          </ion-row>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                  </ion-card-content>
                </ion-card>

                <!-- Comments Card Shimmer -->
                <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
                  <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                    <div class="shimmer-section-title shimmer-wave" style="width: 75px; height: 16px; border-radius: 4px;"></div>
                    <div class="shimmer-edit-icon shimmer-wave" style="width: 18px; height: 18px; border-radius: 50%;"></div>
                  </div>
                  <ion-card-content style="padding: 16px;">
                    <ion-grid style="padding: 0;">
                      <ion-row>
                        <ion-col size="12">
                          <div class="shimmer-field-value shimmer-wave" style="width: 90%; height: 14px; border-radius: 2px; margin-bottom: 4px;"></div>
                          <div class="shimmer-field-value shimmer-wave" style="width: 70%; height: 14px; border-radius: 2px;"></div>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                  </ion-card-content>
                </ion-card>

                <!-- Attachments Card Shimmer -->
                <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
                  <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; min-height: 43px; display: flex; align-items: center;">
                    <div class="shimmer-section-title shimmer-wave" style="width: 120px; height: 16px; border-radius: 4px;"></div>
                  </div>
                  <ion-item lines="none" style="--padding-start: 6px !important;">
                    <span class="uploadImages">
                      <ion-card mode="md" style="min-width: 100px; height: 100px; border: 1px solid #dbdbdb; padding: 4px; margin: 8px 4px; border-radius: 5px; box-shadow: none;">
                        <div class="shimmer-attachment shimmer-wave" style="width: 100%; height: 100%; border-radius: 3px;"></div>
                      </ion-card>
                      <ion-card mode="md" style="min-width: 100px; height: 100px; border: 1px solid #dbdbdb; padding: 4px; margin: 8px 4px; border-radius: 5px; box-shadow: none;">
                        <div class="shimmer-attachment shimmer-wave" style="width: 100%; height: 100%; border-radius: 3px;"></div>
                      </ion-card>
                      <ion-card mode="md" style="min-width: 100px; height: 100px; border: 1px solid #dbdbdb; padding: 4px; margin: 8px 4px; border-radius: 5px; box-shadow: none;">
                        <div class="shimmer-attachment shimmer-wave" style="width: 100%; height: 100%; border-radius: 3px;"></div>
                      </ion-card>
                    </span>
                  </ion-item>
                </ion-card>
              </ion-col>

              <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="6" sizeXl="6">
                <!-- Partners Card Shimmer -->
                <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
                  <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; min-height: 43px; display: flex; align-items: center;">
                    <div class="shimmer-section-title shimmer-wave" style="width: 70px; height: 16px; border-radius: 4px;"></div>
                  </div>
                  <ion-card-content style="padding: 0;">
                    <ion-grid style="margin: 0; font-size: 14px; padding: 0;">
                      <!-- Table Header Shimmer -->
                      <ion-row style="background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; padding: 8px 16px; align-items: center;">
                        <ion-col size="1" style="text-align: center;">
                          <div class="shimmer-table-header shimmer-wave" style="width: 40px; height: 11px; border-radius: 2px;"></div>
                        </ion-col>
                        <ion-col size="0.3"></ion-col>
                        <ion-col size="2.5" style="padding-left: 10px;">
                          <div class="shimmer-table-header shimmer-wave" style="width: 35px; height: 11px; border-radius: 2px;"></div>
                        </ion-col>
                        <ion-col size="4" style="text-align: left; padding-left: 16px;">
                          <div class="shimmer-table-header shimmer-wave" style="width: 35px; height: 11px; border-radius: 2px;"></div>
                        </ion-col>
                        <ion-col size="4.2" style="text-align: left;">
                          <div class="shimmer-table-header shimmer-wave" style="width: 55px; height: 11px; border-radius: 2px;"></div>
                        </ion-col>
                      </ion-row>

                      <!-- Table Rows Shimmer -->
                      <ion-row style="padding: 2px 16px; border-bottom: 1px solid #f1f5f9; min-height: 40px; align-items: center; background-color: white;">
                        <ion-col size="1" style="text-align: center; margin: auto;">
                          <div class="shimmer-seq-number shimmer-wave" style="width: 12px; height: 14px; border-radius: 2px;"></div>
                        </ion-col>
                        <ion-col size="0.3" style="margin: auto; text-align: center;">
                          <div class="shimmer-status-dot shimmer-wave" style="width: 8px; height: 8px; border-radius: 50%;"></div>
                        </ion-col>
                        <ion-col size="2.5" style="margin: auto; padding-left: 10px;">
                          <div style="margin-bottom: 2px;">
                            <div class="shimmer-role-text shimmer-wave" style="width: 50px; height: 14px; border-radius: 2px;"></div>
                          </div>
                          <div class="shimmer-approval-chip shimmer-wave" style="width: 40px; height: 18px; border-radius: 10px;"></div>
                        </ion-col>
                        <ion-col size="4" style="margin: auto; padding-left: 16px;">
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="shimmer-user-name shimmer-wave" style="width: 80px; height: 14px; border-radius: 2px;"></div>
                            <div class="shimmer-skill-icon shimmer-wave" style="width: 16px; height: 16px; border-radius: 2px;"></div>
                          </div>
                        </ion-col>
                        <ion-col size="4.2" style="display: flex; align-items: center; justify-content: flex-start; min-height: 40px;">
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="shimmer-action-button shimmer-wave" style="width: 60px; height: 28px; border-radius: 6px;"></div>
                            <div class="shimmer-status-chip shimmer-wave" style="width: 50px; height: 20px; border-radius: 10px;"></div>
                          </div>
                        </ion-col>
                      </ion-row>

                      <ion-row style="padding: 2px 16px; border-bottom: 1px solid #f1f5f9; min-height: 40px; align-items: center; background-color: white;">
                        <ion-col size="1" style="text-align: center; margin: auto;">
                          <div class="shimmer-seq-number shimmer-wave" style="width: 12px; height: 14px; border-radius: 2px;"></div>
                        </ion-col>
                        <ion-col size="0.3" style="margin: auto; text-align: center;">
                          <div class="shimmer-status-dot shimmer-wave" style="width: 8px; height: 8px; border-radius: 50%;"></div>
                        </ion-col>
                        <ion-col size="2.5" style="margin: auto; padding-left: 10px;">
                          <div style="margin-bottom: 2px;">
                            <div class="shimmer-role-text shimmer-wave" style="width: 45px; height: 14px; border-radius: 2px;"></div>
                          </div>
                        </ion-col>
                        <ion-col size="4" style="margin: auto; padding-left: 16px;">
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="shimmer-user-name shimmer-wave" style="width: 90px; height: 14px; border-radius: 2px;"></div>
                            <div class="shimmer-skill-icon shimmer-wave" style="width: 16px; height: 16px; border-radius: 2px;"></div>
                          </div>
                        </ion-col>
                        <ion-col size="4.2" style="display: flex; align-items: center; justify-content: flex-start; min-height: 40px;">
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="shimmer-action-button shimmer-wave" style="width: 55px; height: 28px; border-radius: 6px;"></div>
                            <div class="shimmer-action-button shimmer-wave" style="width: 50px; height: 28px; border-radius: 6px;"></div>
                          </div>
                        </ion-col>
                      </ion-row>

                      <ion-row style="padding: 2px 16px; border-bottom: 1px solid #f1f5f9; min-height: 40px; align-items: center; background-color: white;">
                        <ion-col size="1" style="text-align: center; margin: auto;">
                          <div class="shimmer-seq-number shimmer-wave" style="width: 12px; height: 14px; border-radius: 2px;"></div>
                        </ion-col>
                        <ion-col size="0.3" style="margin: auto; text-align: center;">
                          <div class="shimmer-status-dot shimmer-wave" style="width: 8px; height: 8px; border-radius: 50%;"></div>
                        </ion-col>
                        <ion-col size="2.5" style="margin: auto; padding-left: 10px;">
                          <div style="margin-bottom: 2px;">
                            <div class="shimmer-role-text shimmer-wave" style="width: 55px; height: 14px; border-radius: 2px;"></div>
                          </div>
                        </ion-col>
                        <ion-col size="4" style="margin: auto; padding-left: 16px;">
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="shimmer-user-name shimmer-wave" style="width: 75px; height: 14px; border-radius: 2px;"></div>
                            <div class="shimmer-skill-icon shimmer-wave" style="width: 16px; height: 16px; border-radius: 2px;"></div>
                          </div>
                        </ion-col>
                        <ion-col size="4.2" style="display: flex; align-items: center; justify-content: flex-start; min-height: 40px;">
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="shimmer-status-chip shimmer-wave" style="width: 65px; height: 20px; border-radius: 10px;"></div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                  </ion-card-content>
                </ion-card>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ng-container>

        <!-- Actual Content (hidden during loading) -->
        <ng-container *ngIf="!isDetailsLoading">
          <ion-grid>
            <ion-row style="margin-top: 0px; font-size: 14px;">
            <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="6" sizeXl="6">

            <!-- General Card -->
            <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
              <!-- Section Header -->
              <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #6366f1;">General</h3>
                <ion-button fill="clear" size="small"
                  *ngIf="(permit.STATUS == 'OPEN') && getHolderApproveCount() && !isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R'"
                  (click)="editPermitBasicDetails()" style="--color: #6366f1; margin: 0;">
                  <ion-icon name="create-outline" slot="icon-only" style="font-size: 18px;"></ion-icon>
                </ion-button>
              </div>
              <ion-card-content style="padding: 16px;">
                <ion-grid style="padding: 0;">
                  <ion-row>
                    <ion-col size="12">

                      <ion-row style="margin-bottom: 12px;">
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Status</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500; display: flex; align-items: flex-start;">
                            <ion-chip [color]="getStatusColor(permit.STATUS)"
                              style="font-size: 12px; height: 24px; --padding-start: 8px; --padding-end: 8px; margin: 0;">
                              {{permit.STATUS | StatusTextFormatFilterPipe}}
                            </ion-chip>
                          </div>
                        </ion-col>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Permit Type</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{permit.PERMIT_TYPE | GetPermitDescriptionPipe | async}}
                          </div>
                        </ion-col>
                      </ion-row>

                      <ion-row style="margin-bottom: 12px;">
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Facility</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{facilityName}}
                          </div>
                        </ion-col>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Division</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{permit.DIVISION_ID | GetPermitDivisionNamePipe | async}}
                          </div>
                        </ion-col>
                      </ion-row>

                      <ion-row style="margin-bottom: 12px;">
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Tag</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500; display: flex; align-items: center; gap: 8px;">
                            <span>{{permit.TAG | GetTagNamePipe | async}}</span>
                            <ion-icon name="location-outline"
                              [style.font-size]="'16px'"
                              [style.color]="hasCoordinates ? '#3880ff' : '#c0c0c0'"
                              [style.cursor]="hasCoordinates ? 'pointer' : 'not-allowed'"
                              [style.opacity]="hasCoordinates ? '1' : '0.5'"
                              (click)="onLocationClick()"
                              [title]="hasCoordinates ? 'View location on map' : 'Location coordinates not available'">
                            </ion-icon>
                            <ion-icon name="navigate-outline"
                              [style.font-size]="'16px'"
                              [style.color]="hasCoordinates ? '#10b981' : '#c0c0c0'"
                              [style.cursor]="hasCoordinates ? 'pointer' : 'not-allowed'"
                              [style.opacity]="hasCoordinates ? '1' : '0.5'"
                              (click)="onNavigateClick()"
                              [title]="hasCoordinates ? 'Navigate to location' : 'Location coordinates not available'">
                            </ion-icon>
                          </div>
                        </ion-col>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Job Number</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{permit.JOB_NO ? permit.JOB_NO : '-'}}
                          </div>
                        </ion-col>
                      </ion-row>

                      <ion-row>
                        <ion-col size="12">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Description</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{permit.DESCRIPTION}}
                          </div>
                        </ion-col>
                      </ion-row>

                      <!-- <ion-row>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Source</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            -
                          </div>
                        </ion-col>
                      </ion-row> -->
                    </ion-col>
                  </ion-row>
                </ion-grid>
              </ion-card-content>
            </ion-card>

            <!-- Agents Card -->
            <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
              <!-- Section Header -->
              <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #6366f1;">Agents</h3>
                <ion-button fill="clear" size="small"
                  *ngIf="(permit.STATUS == 'OPEN' || permit.STATUS == 'IN_REVIEW') && getHolderApproveCount() && !isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R'"
                  (click)="editPermitAgents()" style="--color: #6366f1; margin: 0;">
                  <ion-icon name="create-outline" slot="icon-only" style="font-size: 18px;"></ion-icon>
                </ion-button>
              </div>
              <ion-card-content style="padding: 16px;">
                <ion-grid style="padding: 0;">
                  <ion-row>
                    <ion-col size="12">

                      <ion-row>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Internal</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{(permit.AGENT_ID_INT | GetAgentNamePipe | async) ? (permit.AGENT_ID_INT | GetAgentNamePipe | async) : '-'}}
                          </div>
                        </ion-col>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">External</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{(permit.AGENT_ID_EXT | GetAgentNamePipe | async) ? (permit.AGENT_ID_EXT | GetAgentNamePipe | async) : '-'}}
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>
                </ion-grid>
              </ion-card-content>
            </ion-card>

            <!-- Validity Card -->
            <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
              <!-- Section Header -->
              <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #6366f1;">Validity</h3>
                <ion-button fill="clear" size="small"
                  *ngIf="(permit.STATUS == 'OPEN' || permit.STATUS == 'IN_REVIEW') && getHolderApproveCount() && !isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R'"
                  (click)="editPermitValidity()" style="--color: #6366f1; margin: 0;">
                  <ion-icon name="create-outline" slot="icon-only" style="font-size: 18px;"></ion-icon>
                </ion-button>
              </div>
              <ion-card-content style="padding: 16px;">
                <ion-grid style="padding: 0;">
                  <ion-row>
                    <ion-col size="12">

                      <ion-row>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">Start</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{permit.PERMIT_DATE | GetFullDateByTimestampPipe | async}}
                          </div>
                        </ion-col>
                        <ion-col size="6">
                          <div style="margin-bottom: 4px;">
                            <span style="font-size: 12px; color: #9ca3af; font-weight: 500;">End</span>
                          </div>
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{permit.EXPIRY_DATE | GetFullDateByTimestampPipe | async}}
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>
                </ion-grid>
              </ion-card-content>
            </ion-card>

            <ion-card
              *ngIf="isPermitIsExpired(permit) && isWithin8Hours() && permit.STATUS == 'ISSUED' || (permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true')"
              style="margin-inline: 0px !important;border-radius: 8px;border: 1px solid gainsboro;box-shadow: none;padding-left: 10px;">
              <ion-note color="dark" style="font-weight: 600;
                    margin: 10px 10px 0px 10px;
                    padding-top: 5px;
                    display: block;">Extend</ion-note>

              <ion-grid>
                <ion-row>
                  <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="12" sizeXl="12" style="margin: auto;"
                    *ngIf="permit.EXTENSION_DATE == null">
                    <ion-note>Do you want to extend validity of this permit?</ion-note><br>
                    <ion-toggle [disabled]="!isToggleEnabled" (ionChange)="permit.P_MODE='M';setMinDate()"
                      [disabled]="(permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true') && permit.P_MODE == null && permit.EXTENSION_DATE != null"
                      style="margin-top: 10px;" [enableOnOffLabels]="true"
                      [(ngModel)]="permit.IS_EXTENDED"></ion-toggle>
                  </ion-col>
                  <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="12" sizeXl="12" style="margin: auto;"
                    *ngIf="permit.EXTENSION_DATE != null">
                    <ion-text>
                      <ion-note color="primary"
                        *ngIf="permit.IS_EXTENDED == true && permit.EXTENSION_DATE != null">Permit extended till {{
                        permit.EXTENSION_DATE | date: 'MMM d, y, h:mm a' }} </ion-note>
                    </ion-text>
                  </ion-col>



                </ion-row>
                <ion-row>


                  <!-- Extension Start Date -->
                  <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
                    <div [formGroup]="extensionForm" *ngIf="permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true'">
                      <ion-item button="true" id="start-date-input" lines="none" class="date-control"
                        [ngClass]="{'readonly-item': permit.EXTENSION_DATE != null}"
                        [readonly]="(permit.IS_EXTENDED === true || permit.IS_EXTENDED === 'true') && permit.P_MODE === null && permit.EXTENSION_DATE !== null">
                        <ion-icon style="margin-bottom: 0px; margin-top: 0px; margin-inline-end: 15px;" slot="start"
                          name="today-outline">
                        </ion-icon>
                        <ion-label style="font-size: small;">
                          Extension Start Time <ion-text color="danger">*</ion-text>
                        </ion-label>
                        <ion-text slot="end" style="font-size: small;">
                          {{ extensionForm.controls['startDate'].value | date: 'MMM d, y, h:mm a' }}
                        </ion-text>
                        <ion-popover #startDateTimeModal trigger="start-date-input" show-backdrop="true">
                          <ng-template>
                            <ion-datetime #startPopoverDatetime formControlName="startDate"
                              [value]="extensionForm.controls['startDate'].value"
                              (ionChange)="onStartDateChange($event, startPopoverDatetime.value)">
                            </ion-datetime>
                          </ng-template>
                        </ion-popover>
                      </ion-item>
                    </div>
                  </ion-col>

                  <!-- Extension End Date -->
                  <!-- <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="6" sizeXl="6" style="margin: auto;">
                  
                    </ion-col> -->
                  <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
                    <div [formGroup]="extensionForm" *ngIf="permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true'">
                      <ion-item lines="none" button="true" id="end-date-input" class="date-control"
                        [ngClass]="{'readonly-item': permit.EXTENSION_DATE != null}"
                        [readonly]="(permit.IS_EXTENDED === true || permit.IS_EXTENDED === 'true') && permit.P_MODE === null && permit.EXTENSION_DATE !== null">

                        <ion-icon style="margin-bottom: 0px; margin-top: 0px; margin-inline-end: 15px;" slot="start"
                          name="today-outline"></ion-icon>
                        <ion-label style="font-size: small;">
                          Extension End Time <ion-text color="danger">*</ion-text>
                        </ion-label>
                        <ion-text slot="end" style="font-size: small;" *ngIf="permit.EXTENSION_DATE == null">
                          {{ extensionForm.controls['endDate'].value | date: 'MMM d, y, h:mm a' }}
                        </ion-text>
                        <ion-text slot="end" style="font-size: small;" *ngIf="permit.EXTENSION_DATE != null">
                          {{ permit.EXTENSION_DATE | date: 'MMM d, y, h:mm a' }}
                        </ion-text>
                        <ion-popover #endDateTimeModal trigger="end-date-input" show-backdrop="true">
                          <ng-template>
                            <ion-datetime #endPopoverDatetime formControlName="endDate" interface="popover"
                              [min]="minEndDate" [max]="maxEndDate"
                              (ionChange)="onEndDateChange($event)"></ion-datetime>
                          </ng-template>
                        </ion-popover>
                      </ion-item>
                    </div>
                  </ion-col>


                </ion-row>




                <!-- End Date -->
                <!-- <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6" 
          *ngIf="permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true'"> -->
                <!-- extended date time input  -->
                <!-- <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="6" sizeXl="6"
                    *ngIf="permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true'">
                    <ion-note>Extend Until</ion-note><br>
                    <ion-item lines="none" button="true"
                      [disabled]="(permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true') && permit.P_MODE == null && permit.EXTENSION_DATE != null"
                      id="end-date-input" class="date-control">
                      <ion-icon style="margin-bottom: 0px;margin-top: 0px;margin-inline-end: 15px;" slot="start"
                        name="today-outline"></ion-icon>
                      <ion-label style="font-size: small;">Extension Date <ion-text
                          color="danger">*</ion-text></ion-label>
                      <ion-text slot="end" style="font-size: small;">{{permit.EXTENSION_DATE |
                        GetFullDateByTimestampPipe | async}}</ion-text>
                      <ion-popover #endDateTimeModal trigger="end-date-input" show-backdrop="true">
                        <ng-template>
                          <ion-datetime #endPopoverDatetime
                            (ionChange)="endPopoverDatetime.value;"></ion-datetime>
                        </ng-template>
                      </ion-popover>
                    </ion-item>
                  </ion-col> -->
              </ion-grid>
            </ion-card>

            <!-- Comments Card -->
            <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
              <!-- Section Header -->
              <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #6366f1;">Comments</h3>
                <ion-button fill="clear" size="small"
                  *ngIf="(permit.STATUS == 'OPEN' || permit.STATUS == 'IN_REVIEW') && getHolderApproveCount() && !isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R'"
                  (click)="editComments()" style="--color: #6366f1; margin: 0;">
                  <ion-icon name="create-outline" slot="icon-only" style="font-size: 18px;"></ion-icon>
                </ion-button>
              </div>
              <ion-card-content style="padding: 16px;">
                <ion-grid style="padding: 0;">
                  <ion-row>
                    <ion-col size="12">

                      <ion-row>
                        <ion-col size="12">
                          <div style="font-size: 14px; color: #374151; font-weight: 500;">
                            {{permit.COMMENTS ? permit.COMMENTS : '-'}}
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>
                </ion-grid>
              </ion-card-content>
            </ion-card>

            <!-- Attachments Card -->
            <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
              <!-- Section Header -->
              <div style="background: #e8e5ff; padding: 10px 16px; margin: 0; min-height: 43px; display: flex; align-items: center;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #6366f1;">Attachments ({{permitDocs.length}})</h3>
              </div>
              <ion-item lines="none" style="--padding-start: 6px !important;">
                <span class="uploadImages">
                  <ion-card mode="md" *ngIf="isMobile">
                    <ion-button class="uploadButton" (click)="captureImage()">
                      <label><ion-icon name="camera-outline"
                          style="color: #00629b;cursor:pointer;font-size: xx-large;"></ion-icon></label>
                    </ion-button>
                  </ion-card>
                  <ion-card
                    *ngIf="!isReport && permit.STATUS != 'CLOSED' && permit.STATUS != 'CANCELLED' && !isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R'"
                    mode="md" (click)="file1.click();$event.stopPropagation();">
                    <ion-button class="uploadButton" (change)="uploadFile(file1.files);$event.stopPropagation();">
                      <label><ion-icon name="add-outline"
                          style="color: #00629b;cursor:pointer;font-size: xx-large;"></ion-icon></label>
                      <input #file1 [id]="'F'"
                        accept=".doc, .docx, .pdf, .xls, .xlsx, .ppt, .pptx, .csv, .txt, .png, .jpeg, .jpg" type="file"
                        multiple="true" />
                    </ion-button>
                  </ion-card>
                  <ion-card style="cursor: pointer" *ngFor="let image of permitDocs?.slice().reverse();index as i"
                    mode="md">

                    <ion-icon *ngIf="image?.docItem?.P_MODE == 'A' || !image?.docItem" name="trash" color="danger"
                      (click)="permitDocs.splice(findIndex(permitDocs, image), 1)"
                      style="position: absolute;right: 0;margin-right: 3px;margin-top: 3px;font-size: 19px;cursor: pointer">
                    </ion-icon>


                    <img *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                      src="../../../assets/images/excel.png" (click)="downloadFile(image)">
                    <img *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                      src="../../../assets/images/powerpoint.png" (click)="downloadFile(image)">


                    <img *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'WORD'"
                      src="../../../assets/images/word.png" (click)="downloadFile(image)">
                    <img *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'DOCUMENT'"
                      src="../../../assets/images/word.png" (click)="downloadFile(image)">
                    <img *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                      src="../../../assets/images/pdf.png" (click)="downloadFile(image)">
                    <img *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                      src="../../../assets/images/text.png" (click)="downloadFile(image)">
                    <img *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                      src="../../../assets/images/csv.png" (click)="downloadFile(image)">
                    <img *ngIf="image?.DOC_TYPE == 'IMAGE'"
                      (click)=" image?.docItem ? imageInFullscreen(image,'Permit Image: ',i): editImage(image, i)"
                      [src]="image.thumbnail">





                  </ion-card>
                </span>
              </ion-item>
            </ion-card>
          </ion-col>

          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="12" sizeLg="6" sizeXl="6">


            <!-- <ion-segment style="margin: 0px 25px 0px 20px;width: auto;" mode="md" *ngIf="isPermitRevised"  [(ngModel)]="selectedSegment">
              <ion-segment-button value="default">
                <ion-label style="font-weight: 600;">Partners</ion-label>
              </ion-segment-button>
              <ion-segment-button value="segment">
                <ion-label style="font-weight: 600;">History</ion-label>
              </ion-segment-button>
            </ion-segment>
           -->

            <!-- Partners Card -->
            <ion-card style="margin: 0 0 12px 0; border-radius: 8px; border: 1px solid #e0e0e0; box-shadow: none; background: white; overflow: hidden;">
              <!-- Tab segment for revised permits - Updated to match main tabs styling -->
              <div *ngIf="isPermitRevised" style="background: #e8e5ff; padding: 10px 16px; margin: 0; display: flex; justify-content: space-between; align-items: center; min-height: 43px;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #6366f1;">Partners</h3>
                <ion-segment [(ngModel)]="selectedSegment" style="margin: 0; width: 200px; justify-content: flex-end;" mode="md">
                  <ion-segment-button value="default" style="min-width: 80px; width: 100px;">
                    <ion-label style="font-weight: 600;">Current</ion-label>
                  </ion-segment-button>
                  <ion-segment-button value="segment" style="min-width: 80px; width: 100px;">
                    <ion-label style="font-weight: 600;">History</ion-label>
                  </ion-segment-button>
                </ion-segment>
              </div>

              <!-- Section Header for non-revised permits -->
              <div *ngIf="!isPermitRevised" style="background: #e8e5ff; padding: 10px 16px; margin: 0; min-height: 43px; display: flex; align-items: center;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #6366f1;">Partners</h3>
              </div>

              <ion-card-content style="padding: 0;">

            <div *ngIf="!isPermitRevised">
              <ion-grid style="margin: 0; font-size: 14px; padding: 0;">
                <ion-row style="background-color: #f9fafb;
                color: #9ca3af;
                font-weight: 500;
                font-size: 11px;
                text-transform: uppercase;
                letter-spacing: 0.8px;
                border-bottom: 1px solid #e5e7eb;
                padding: px 16px;
                align-items: center;">
                  <ion-col size="1" style="text-align: center;">SEQ NO</ion-col>
                  <ion-col size="0.3"></ion-col>
                  <ion-col size="2.5" style="padding-left: 10px;">ROLE</ion-col>
                  <ion-col size="4" style="text-align: left; padding-left: 16px;">USER</ion-col>
                  <ion-col size="4.2" style="text-align: left;">ACTIONS</ion-col>
                </ion-row>




                <div *ngIf="!isPartnersLoading">
                  <ion-row *ngFor="let stakeHolder of stakeHoldersList; index as i" class="row-hover"
                    (click)="isUserClickable(stakeHolder) ? addNewStakeHolderRole(true, i , stakeHolder) : null"
                    style="padding: 2px 16px; border-bottom: 1px solid #f1f5f9; min-height: 40px; align-items: center; background-color: white;"
                    [style.cursor]="isUserClickable(stakeHolder) ? 'pointer' : 'default'">

                    <!-- Sequence Number Column -->
                    <ion-col size="1" style="text-align: center; margin: auto;">
                      <span style="font-size: 14px; color: #64748b; font-weight: 400;">{{i + 1}}</span>
                    </ion-col>

                    <!-- Status Icon Column -->
                    <ion-col size="0.3" style="margin: auto; text-align: center;">
                      <ion-icon *ngIf="stakeHolder.P_MODE=='A'" style="font-size: 8px;color:darkgreen"
                        name="ellipse"></ion-icon>
                      <ion-icon *ngIf="stakeHolder.P_MODE=='M'" style="font-size: 8px;color:#ffc409"
                        name="ellipse"></ion-icon>
                      <ion-icon *ngIf="stakeHolder.P_MODE==null" style="font-size: 8px;color:transparent"
                        name="ellipse-outline"></ion-icon>
                    </ion-col>

                    <!-- Role Column -->
                    <ion-col size="2.5" style="margin: auto; display: flex; flex-direction: column; align-items: flex-start; padding-left: 10px;">
                      <div style="font-size: 14px; font-weight: 600; color: #1f2937; line-height: 1.2; margin-bottom: 1px;">
                        {{ stakeHolder.ROLE | UppercaseToLowerCaseFilter}}
                      </div>
                      <ion-chip *ngIf="stakeHolder.APPR_TYPE"
                        style="padding: 1px 6px; min-height: 18px; --background: #dbeafe; --color: #1e40af; font-size: 10px; font-weight: 500; margin-left: 0; margin-right: 0; border-radius: 10px;">
                        {{ stakeHolder.APPR_TYPE }}
                      </ion-chip>
                    </ion-col>

                    <!-- User Column -->
                    <ion-col size="4" style="margin: auto; padding-left: 16px;">
                      <div *ngIf="isDissabledUserDropdown(stakeHolder)"
                           style="font-size: 14px; color: #374151; font-weight: 400; line-height: 1.2; cursor: default; display: flex; align-items: center; gap: 8px;">
                        <span>{{ getName(stakeHolder.USER_ID) }}</span>
                        <ion-icon
                          *ngIf="stakeHolder.USER_ID"
                          name="school-outline"
                          style="font-size: 16px; color: #6366f1; cursor: pointer;"
                          (click)="showUserSkillsTooltip($event, stakeHolder.USER_ID)"
                          title="View Skills">
                        </ion-icon>
                      </div>

                      <ion-input mode="md"
                        style="--padding-start: 16px !important;min-height: 35px !important ; font-size: medium ; text-align: left;"
                        [value]="getName(stakeHolder.USER_ID)" [readonly]="isDissabledUserDropdown(stakeHolder)"
                        *ngIf="false" labelPlacement="popover">
                      </ion-input>
                      <!-- 
                      <div *ngIf="stakeHolder.ROLE == 'REVIEW' &&  stakeHolder.APPR_TYPE == ''">
                        <ion-icon name="information-circle-outline"></ion-icon>
                      </div>
                    -->





                      <div *ngIf="!isDissabledUserDropdown(stakeHolder)"
                           style="font-size: 14px; font-weight: 400; line-height: 1.2; display: flex; align-items: center; gap: 8px;"
                           [style.color]="isUserClickable(stakeHolder) ? '#6366f1' : '#374151'"
                           [style.cursor]="isUserClickable(stakeHolder) ? 'pointer' : 'default'">
                        <span (click)="onUserTextClick(stakeHolder, i, $event)">
                          {{ getName(stakeHolder.USER_ID) || (isUserClickable(stakeHolder) ? 'Click here to assign' : '') }}
                        </span>
                        <ion-icon
                          *ngIf="stakeHolder.USER_ID"
                          name="school-outline"
                          style="font-size: 16px; color: #6366f1; cursor: pointer;"
                          (click)="showUserSkillsTooltip($event, stakeHolder.USER_ID)"
                          title="View Skills">
                        </ion-icon>
                      </div>



                    </ion-col>



                    <ion-col style="display: flex; align-items: center; justify-content: flex-start; min-height: 40px;" size="4.2">



                      <ion-buttons mode="ios"
                        *ngIf="stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null  && stakeHolder.PROCESSED_ON == null && permit.APPROVAL_SUMMARY != 'R' && checkUserForButtons(stakeHolder) && canShowApproveAndRejectButtonsForReview(stakeHolder) && !isPermitIsExpired(permit)">
                        <div style="display: flex; gap: 8px; min-width: 150px; justify-content: flex-start;">
                          <ion-button fill="solid" size="small" color="success" mode="md"
                            style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                            *ngIf="shouldShowApproveButton(stakeHolder)"
                            (click)="actionButtons('Approved', i, stakeHolder); $event.stopPropagation()">
                            <ion-icon name="checkmark-outline" slot="start" style="font-size: 14px;"></ion-icon>
                            Approve
                          </ion-button>

                          <ion-button fill="solid" size="small" color="danger" mode="md"
                            style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                            *ngIf="shouldShowRejectButton(stakeHolder)"
                            (click)="actionButtons('Rejected', i, stakeHolder); $event.stopPropagation()">
                            <ion-icon name="close-outline" slot="start" style="font-size: 14px;"></ion-icon>
                            Reject
                          </ion-button>

                        </div>
                      </ion-buttons>


                      <!-- ends here -->

                      <ion-col size="1" *ngIf="stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null">

                      </ion-col>


                      <ion-buttons mode="ios"
                        *ngIf="shouldShowRejectButton(stakeHolder) && !isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R' && stakeHolder.PROCESSED_ON == null">
                        <div style="display: flex; gap: 8px; min-width: 150px; justify-content: flex-start;">
                          <ion-button fill="solid" size="small" color="success" mode="md"
                            style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                            *ngIf="stakeHolder.APPROVAL == 'O' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE != null && checkUserForButtons(stakeHolder)"
                            (click)="checkIfFormData('Approved', i, stakeHolder); $event.stopPropagation()">
                            <ion-icon name="checkmark-outline" slot="start" style="font-size: 14px;"></ion-icon>
                            Approve
                          </ion-button>

                          <ion-button fill="solid" size="small" color="danger" mode="md"
                            style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                            *ngIf=" stakeHolder.APPROVAL == 'O' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE != null && checkUserForButtons(stakeHolder) "
                            (click)="checkIfFormData('Rejected', i, stakeHolder); $event.stopPropagation()">
                            <ion-icon name="close-outline" slot="start" style="font-size: 14px;"></ion-icon>
                            Reject
                          </ion-button>
                        </div>
                      </ion-buttons>



                      <ion-row style="display: flex; align-items: center;">
                        <!-- Column for Status and Date -->
                        <ion-col size="auto">
                          <div style="display: flex; align-items: center;">

                            <ion-button style="font-size: 13px;" fill="outline" color="primary" class="refreshButton"
                              *ngIf="stakeHolder.ROLE == 'EXECUTE' && checkUserForButtons(stakeHolder) && permit.STATUS == 'ISSUED' && !this.hidePerformButton(stakeHolder.ROW_ID) && !isPermitIsExpired(permit) "
                              (click)="checkIfFormData('Executed', i, stakeHolder); $event.stopPropagation()"
                              class="approveActionButton refreshButton">
                              Perform
                            </ion-button>

                            <ion-button style="font-size: 13px;" fill="outline" color="primary" class="refreshButton"
                              *ngIf="  checkUserForButtons(stakeHolder) && stakeHolder.ROLE == 'APPROVE'  && !this.hideConfirmButton(stakeHolder.ROW_ID) && !isPermitIsExpired(permit) "
                              (click)="checkIfFormData('Confirmed', i, stakeHolder); $event.stopPropagation()"
                              class="approveActionButton refreshButton">
                              Confirm
                            </ion-button>

                            <ion-chip style="padding: 2px 8px; min-height: 20px; border-radius: 10px; font-size: 11px; font-weight: 500;"
                                     [color]="(stakeHolder.ROLE === 'EXECUTE' || stakeHolder.ROLE === 'APPROVE' || stakeHolder.ROLE === 'ISSUE' ) ? 'primary' :
                                    (stakeHolder.ROLE === 'REVIEW' && stakeHolder.APPROVAL === 'A') ? 'success' :
                                    stakeHolder.APPROVAL === 'R' ? 'danger' :
                                    getChipColor(getActionStatus(stakeHolder))">

                              {{ (stakeHolder.ROLE === 'EXECUTE' && stakeHolder.PROCESSED_ON != null) ? 'Executed' :
                              (stakeHolder.ROLE === 'REVIEW' && stakeHolder.APPROVAL === 'A') ? 'Approved' :
                              (stakeHolder.ROLE === 'ISSUE' && stakeHolder.PROCESSED_ON != null) ? 'Issued' :
                              (stakeHolder.ROLE === 'APPROVE' && stakeHolder.PROCESSED_ON != null) ? 'Confirmed' :
                              stakeHolder.APPROVAL === 'R' ? 'Rejected' :
                              getActionStatus(stakeHolder) }}
                            </ion-chip>


                          </div>

                          <!-- Date Below the Status -->
                          <div *ngIf="stakeHolder.PROCESSED_ON != null"
                               style="font-size: 10px; color: #9ca3af; margin-top: 2px; text-align: left;">
                            {{ stakeHolder.PROCESSED_ON * 1000 | date:'MMM d, y h:mm a' }}
                          </div>

                        </ion-col>


                      </ion-row>

                    </ion-col>

                  </ion-row>

                </div>

              </ion-grid>
              <div style="margin-top: 16px; text-align: center; padding: 0 16px;">
                <ion-button
                  *ngIf="!isReport && !isHideNewStakeHolderButton && permit.STATUS != 'CLOSED' && permit.STATUS != 'CANCELLED' && permit.STATUS != 'OPEN' && !isPermitIsExpired(permit)"
                  fill="outline" color="primary" mode="md" size="small"
                  style="--border-radius: 6px; font-weight: 500; --padding-start: 16px; --padding-end: 16px; height: 36px; font-size: 13px;"
                  (click)="addNewStakeHolderRole(false , 0 , {} )">
                  <ion-icon slot="start" name="add-outline" style="font-size: 14px;"></ion-icon>
                  Add New
                </ion-button>
              </div>
            </div>

            <div *ngIf="isPermitRevised">
              <!-- Current Partners Tab -->
              <div *ngIf="selectedSegment == 'default'">
                <ion-grid style="margin: 0; font-size: 14px; padding: 0;">
                  <ion-row style="background-color: #f9fafb;
                  color: #9ca3af;
                  font-weight: 500;
                  font-size: 11px;
                  text-transform: uppercase;
                  letter-spacing: 0.8px;
                  border-bottom: 1px solid #e5e7eb;
                  padding: 8px 16px;
                  align-items: center;">
                    <ion-col size="1" style="text-align: center;">SEQ NO</ion-col>
                    <ion-col size="0.3"></ion-col>
                    <ion-col size="2.5" style="padding-left: 10px;">ROLE</ion-col>
                    <ion-col size="4" style="text-align: left; padding-left: 16px;">USER</ion-col>
                    <ion-col size="4.2" style="text-align: left;">ACTIONS</ion-col>
                  </ion-row>

                  <div *ngIf="!isPartnersLoading">
                    <ng-container *ngFor="let stakeHolder of currentStakeholders; index as i">
                      <ion-row
                               class="row-hover"
                               (click)="isUserClickable(stakeHolder) ? addNewStakeHolderRole(true, i , stakeHolder) : null"
                               style="padding: 2px 16px; border-bottom: 1px solid #f1f5f9; min-height: 40px; align-items: center; background-color: white;"
                               [style.cursor]="isUserClickable(stakeHolder) ? 'pointer' : 'default'">

                      <!-- Sequence Number Column -->
                      <ion-col size="1" style="text-align: center; margin: auto;">
                        <span style="font-size: 14px; color: #64748b; font-weight: 400;">{{i + 1}}</span>
                      </ion-col>

                      <!-- Status Icon Column -->
                      <ion-col size="0.3" style="margin: auto; text-align: center;">
                        <ion-icon *ngIf="stakeHolder.P_MODE=='A'" style="font-size: 8px;color:darkgreen"
                          name="ellipse"></ion-icon>
                        <ion-icon *ngIf="stakeHolder.P_MODE=='M'" style="font-size: 8px;color:#ffc409"
                          name="ellipse"></ion-icon>
                        <ion-icon *ngIf="stakeHolder.P_MODE==null" style="font-size: 8px;color:transparent"
                          name="ellipse-outline"></ion-icon>
                      </ion-col>

                      <!-- Role Column -->
                      <ion-col size="2.5" style="margin: auto; display: flex; flex-direction: column; align-items: flex-start; padding-left: 10px;">
                        <div style="font-size: 14px; font-weight: 600; color: #1f2937; line-height: 1.2; margin-bottom: 1px;">
                          {{ stakeHolder.ROLE | UppercaseToLowerCaseFilter}}
                        </div>
                        <ion-chip *ngIf="stakeHolder.APPR_TYPE"
                          style="padding: 1px 6px; min-height: 18px; --background: #dbeafe; --color: #1e40af; font-size: 10px; font-weight: 500; margin-left: 0; margin-right: 0; border-radius: 10px;">
                          {{ stakeHolder.APPR_TYPE }}
                        </ion-chip>
                      </ion-col>
                      <!-- User Column -->
                      <ion-col size="4" style="margin: auto; padding-left: 16px;">
                        <div *ngIf="isDissabledUserDropdown(stakeHolder)"
                             style="font-size: 14px; color: #374151; font-weight: 400; line-height: 1.2; cursor: default; display: flex; align-items: center; gap: 8px;">
                          <span>{{ getName(stakeHolder.USER_ID) }}</span>
                          <ion-icon
                            *ngIf="stakeHolder.USER_ID"
                            name="school-outline"
                            style="font-size: 16px; color: #6366f1; cursor: pointer;"
                            (click)="showUserSkillsTooltip($event, stakeHolder.USER_ID)"
                            title="View Skills">
                          </ion-icon>
                        </div>

                        <div *ngIf="!isDissabledUserDropdown(stakeHolder)"
                             style="font-size: 14px; font-weight: 400; line-height: 1.2; display: flex; align-items: center; gap: 8px;"
                             [style.color]="isUserClickable(stakeHolder) ? '#6366f1' : '#374151'"
                             [style.cursor]="isUserClickable(stakeHolder) ? 'pointer' : 'default'">
                          <span (click)="onUserTextClick(stakeHolder, i, $event)">
                            {{ getName(stakeHolder.USER_ID) || (isUserClickable(stakeHolder) ? 'Click here to assign' : '') }}
                          </span>
                          <ion-icon
                            *ngIf="stakeHolder.USER_ID"
                            name="school-outline"
                            style="font-size: 16px; color: #6366f1; cursor: pointer;"
                            (click)="showUserSkillsTooltip($event, stakeHolder.USER_ID)"
                            title="View Skills">
                          </ion-icon>
                        </div>
                      </ion-col>



                      <!-- Actions Column -->
                      <ion-col style="display: flex; align-items: center; justify-content: flex-start; min-height: 40px;" size="4.2">
                        <ion-buttons mode="ios"
                          *ngIf="stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null  && stakeHolder.PROCESSED_ON == null && permit.APPROVAL_SUMMARY != 'R' && checkUserForButtons(stakeHolder) && canShowApproveAndRejectButtonsForReview(stakeHolder) && !isPermitIsExpired(permit)">
                          <div style="display: flex; gap: 8px; min-width: 150px; justify-content: flex-start;">
                            <ion-button fill="solid" size="small" color="success" mode="md"
                              style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                              *ngIf="shouldShowApproveButton(stakeHolder)"
                              (click)="actionButtons('Approved', i, stakeHolder); $event.stopPropagation()">
                              <ion-icon name="checkmark-outline" slot="start" style="font-size: 14px;"></ion-icon>
                              Approve
                            </ion-button>

                            <ion-button fill="solid" size="small" color="danger" mode="md"
                              style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                              *ngIf="shouldShowRejectButton(stakeHolder)"
                              (click)="actionButtons('Rejected', i, stakeHolder); $event.stopPropagation()">
                              <ion-icon name="close-outline" slot="start" style="font-size: 14px;"></ion-icon>
                              Reject
                            </ion-button>
                          </div>
                        </ion-buttons>

                        <ion-buttons mode="ios"
                          *ngIf="shouldShowRejectButton(stakeHolder) && !isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R' && stakeHolder.PROCESSED_ON == null">
                          <div style="display: flex; gap: 8px; min-width: 150px; justify-content: flex-start;">
                            <ion-button fill="solid" size="small" color="success" mode="md"
                              style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                              *ngIf="stakeHolder.APPROVAL == 'O' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE != null && checkUserForButtons(stakeHolder)"
                              (click)="checkIfFormData('Approved', i, stakeHolder); $event.stopPropagation()">
                              <ion-icon name="checkmark-outline" slot="start" style="font-size: 14px;"></ion-icon>
                              Approve
                            </ion-button>

                            <ion-button fill="solid" size="small" color="danger" mode="md"
                              style="--border-radius: 6px; font-weight: 600; font-size: 12px; --padding-start: 12px; --padding-end: 12px;"
                              *ngIf=" stakeHolder.APPROVAL == 'O' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE != null && checkUserForButtons(stakeHolder) "
                              (click)="checkIfFormData('Rejected', i, stakeHolder); $event.stopPropagation()">
                              <ion-icon name="close-outline" slot="start" style="font-size: 14px;"></ion-icon>
                              Reject
                            </ion-button>
                          </div>
                        </ion-buttons>

                        <ion-row style="display: flex; align-items: center;">
                          <!-- Column for Status and Date -->
                          <ion-col size="auto">
                            <div style="display: flex; align-items: center;">
                              <ion-button style="font-size: 13px;" fill="outline" color="primary" class="refreshButton"
                                *ngIf="stakeHolder.ROLE == 'EXECUTE' && checkUserForButtons(stakeHolder) && permit.STATUS == 'ISSUED' && !this.hidePerformButton(stakeHolder.ROW_ID) && !isPermitIsExpired(permit) "
                                (click)="checkIfFormData('Executed', i, stakeHolder); $event.stopPropagation()"
                                class="approveActionButton refreshButton">
                                Perform
                              </ion-button>

                              <ion-button style="font-size: 13px;" fill="outline" color="primary" class="refreshButton"
                                *ngIf="  checkUserForButtons(stakeHolder) && stakeHolder.ROLE == 'APPROVE'  && !this.hideConfirmButton(stakeHolder.ROW_ID) && !isPermitIsExpired(permit) "
                                (click)="checkIfFormData('Confirmed', i, stakeHolder); $event.stopPropagation()"
                                class="approveActionButton refreshButton">
                                Confirm
                              </ion-button>

                              <ion-chip style="padding: 2px 8px; min-height: 20px; border-radius: 10px; font-size: 11px; font-weight: 500;"
                                       [color]="(stakeHolder.ROLE === 'EXECUTE' || stakeHolder.ROLE === 'APPROVE' || stakeHolder.ROLE === 'ISSUE' ) ? 'primary' :
                                      (stakeHolder.ROLE === 'REVIEW' && stakeHolder.APPROVAL === 'A') ? 'success' :
                                      stakeHolder.APPROVAL === 'R' ? 'danger' :
                                      getChipColor(getActionStatus(stakeHolder))">

                                {{ (stakeHolder.ROLE === 'EXECUTE' && stakeHolder.PROCESSED_ON != null) ? 'Executed' :
                                (stakeHolder.ROLE === 'REVIEW' && stakeHolder.APPROVAL === 'A') ? 'Approved' :
                                (stakeHolder.ROLE === 'ISSUE' && stakeHolder.PROCESSED_ON != null) ? 'Issued' :
                                (stakeHolder.ROLE === 'APPROVE' && stakeHolder.PROCESSED_ON != null) ? 'Confirmed' :
                                stakeHolder.APPROVAL === 'R' ? 'Rejected' :
                                getActionStatus(stakeHolder) }}
                              </ion-chip>
                            </div>

                            <!-- Date Below the Status -->
                            <div *ngIf="stakeHolder.PROCESSED_ON != null"
                                 style="font-size: 10px; color: #9ca3af; margin-top: 2px; text-align: left;">
                              {{ stakeHolder.PROCESSED_ON * 1000 | date:'MMM d, y h:mm a' }}
                            </div>
                          </ion-col>
                        </ion-row>
                      </ion-col>
                    </ion-row>
                    </ng-container>
                  </div>
                </ion-grid>

                <div style="margin-top: 16px; text-align: center; padding: 0 16px;">
                  <ion-button
                    *ngIf="!isReport && !isHideNewStakeHolderButton && permit.STATUS != 'CLOSED' && permit.STATUS != 'CANCELLED' && permit.STATUS != 'OPEN' && !isPermitIsExpired(permit) && !checkIfCloseRoleInStakeHolders()"
                    fill="outline" color="primary" mode="md" size="small"
                    style="--border-radius: 6px; font-weight: 500; --padding-start: 16px; --padding-end: 16px; height: 36px; font-size: 13px;"
                    (click)="addNewStakeHolderRole(false , 0 , {} )">
                    <ion-icon slot="start" name="add-outline" style="font-size: 14px;"></ion-icon>
                    Add New
                  </ion-button>
                </div>
              </div>

              <!-- History Partners Tab -->
              <div *ngIf="selectedSegment == 'segment'">
                <ion-grid style="margin: 0; font-size: 14px; padding: 0;">
                  <ion-row style="background-color: #f9fafb;
                  color: #9ca3af;
                  font-weight: 500;
                  font-size: 11px;
                  text-transform: uppercase;
                  letter-spacing: 0.8px;
                  border-bottom: 1px solid #e5e7eb;
                  padding: 8px 16px;
                  align-items: center;">
                    <ion-col size="1" style="text-align: center;">SEQ NO</ion-col>
                    <ion-col size="0.3"></ion-col>
                    <ion-col size="2.5" style="padding-left: 10px;">ROLE</ion-col>
                    <ion-col size="4" style="text-align: left; padding-left: 16px;">USER</ion-col>
                    <ion-col size="4.2" style="text-align: left;">ACTIONS</ion-col>
                  </ion-row>

                  <div *ngIf="!isPartnersLoading">
                    <ng-container *ngFor="let stakeHolder of historyStakeholders; index as i">
                      <ion-row
                               class="row-hover"
                               style="padding: 2px 16px; border-bottom: 1px solid #f1f5f9; min-height: 40px; align-items: center; background-color: white; cursor: default;">

                      <!-- Sequence Number Column -->
                      <ion-col size="1" style="text-align: center; margin: auto;">
                        <span style="font-size: 14px; color: #64748b; font-weight: 400;">{{i + 1}}</span>
                      </ion-col>

                      <!-- Status Icon Column -->
                      <ion-col size="0.3" style="margin: auto; text-align: center;">
                        <ion-icon *ngIf="stakeHolder.P_MODE=='A'" style="font-size: 8px;color:darkgreen"
                          name="ellipse"></ion-icon>
                        <ion-icon *ngIf="stakeHolder.P_MODE=='M'" style="font-size: 8px;color:#ffc409"
                          name="ellipse"></ion-icon>
                        <ion-icon *ngIf="stakeHolder.P_MODE==null" style="font-size: 8px;color:transparent"
                          name="ellipse-outline"></ion-icon>
                      </ion-col>

                      <!-- Role Column -->
                      <ion-col size="2.5" style="margin: auto; display: flex; flex-direction: column; align-items: flex-start; padding-left: 10px;">
                        <div style="font-size: 14px; font-weight: 600; color: #1f2937; line-height: 1.2; margin-bottom: 1px;">
                          {{ stakeHolder.ROLE | UppercaseToLowerCaseFilter}}
                        </div>
                        <ion-chip *ngIf="stakeHolder.APPR_TYPE"
                          style="padding: 1px 6px; min-height: 18px; --background: #dbeafe; --color: #1e40af; font-size: 10px; font-weight: 500; margin-left: 0; margin-right: 0; border-radius: 10px;">
                          {{ stakeHolder.APPR_TYPE }}
                        </ion-chip>
                      </ion-col>

                      <!-- User Column -->
                      <ion-col size="4" style="margin: auto; padding-left: 16px;">
                        <div style="font-size: 14px; color: #374151; font-weight: 400; line-height: 1.2; cursor: default; display: flex; align-items: center; gap: 8px;">
                          <span>{{ getName(stakeHolder.USER_ID) }}</span>
                          <ion-icon
                            *ngIf="stakeHolder.USER_ID"
                            name="school-outline"
                            style="font-size: 16px; color: #6366f1; cursor: pointer;"
                            (click)="showUserSkillsTooltip($event, stakeHolder.USER_ID)"
                            title="View Skills">
                          </ion-icon>
                        </div>
                      </ion-col>



                      <!-- Actions Column (History - Read Only) -->
                      <ion-col style="display: flex; align-items: center; justify-content: flex-start; min-height: 40px;" size="4.2">
                        <ion-row style="display: flex; align-items: center;">
                          <!-- Column for Status and Date -->
                          <ion-col size="auto">
                            <div style="display: flex; align-items: center;">
                              <ion-chip style="padding: 2px 8px; min-height: 20px; border-radius: 10px; font-size: 11px; font-weight: 500;"
                                       [color]="(stakeHolder.ROLE === 'EXECUTE' || stakeHolder.ROLE === 'APPROVE' || stakeHolder.ROLE === 'ISSUE' ) ? 'primary' :
                                      (stakeHolder.ROLE === 'REVIEW' && stakeHolder.APPROVAL === 'A') ? 'success' :
                                      stakeHolder.APPROVAL === 'R' ? 'danger' :
                                      getChipColor(getActionStatus(stakeHolder))">

                                {{ (stakeHolder.ROLE === 'EXECUTE' && stakeHolder.PROCESSED_ON != null) ? 'Executed' :
                                (stakeHolder.ROLE === 'REVIEW' && stakeHolder.APPROVAL === 'A') ? 'Approved' :
                                (stakeHolder.ROLE === 'ISSUE' && stakeHolder.PROCESSED_ON != null) ? 'Issued' :
                                (stakeHolder.ROLE === 'APPROVE' && stakeHolder.PROCESSED_ON != null) ? 'Confirmed' :
                                stakeHolder.APPROVAL === 'R' ? 'Rejected' :
                                getActionStatus(stakeHolder) }}
                              </ion-chip>
                            </div>

                            <!-- Date Below the Status -->
                            <div *ngIf="stakeHolder.PROCESSED_ON != null"
                                 style="font-size: 10px; color: #9ca3af; margin-top: 2px; text-align: left;">
                              {{ stakeHolder.PROCESSED_ON * 1000 | date:'MMM d, y h:mm a' }}
                            </div>
                          </ion-col>
                        </ion-row>
                      </ion-col>
                    </ion-row>
                    </ng-container>
                  </div>
                </ion-grid>
              </div>
            </div>


              </ion-card-content>
            </ion-card>
          </ion-col>
        </ion-row>
      </ion-grid>
        </ng-container>
      </div>
      <!-- End of scrollable content area for Details tab -->
    </div>

    <div *ngSwitchCase="'form'" style="margin-top: 0px;">
      <!-- Scrollable content area for Form tab -->
      <div [ngClass]="isMobile ? 'content-area-mobile form-content' : 'content-area-desktop'" style="height: calc(100vh - 120px); overflow-y: auto; background: #f5f5f5; margin-top: 0px;">

        <!-- Shimmer Loading for Form -->
        <ng-container *ngIf="isFormLoading">
          <div class="shimmer-form-container">
            <div class="shimmer-form-header">
              <div class="shimmer-form-title shimmer"></div>
            </div>
            <div class="shimmer-form-field">
              <div class="shimmer-label shimmer"></div>
              <div class="shimmer-input shimmer"></div>
            </div>
            <div class="shimmer-form-field">
              <div class="shimmer-label shimmer"></div>
              <div class="shimmer-input shimmer"></div>
            </div>
            <div class="shimmer-form-field">
              <div class="shimmer-label shimmer"></div>
              <div class="shimmer-textarea shimmer"></div>
            </div>
            <div class="shimmer-form-field">
              <div class="shimmer-label shimmer"></div>
              <div class="shimmer-input shimmer"></div>
            </div>
          </div>
        </ng-container>

        <!-- Actual Form Content -->
        <ng-container *ngIf="!isFormLoading">
          <div id="formio" [ngClass]="{'isDisable': isReport}" *ngIf="isFormFound"></div>
          <p *ngIf="!isFormFound" class="empty-data-info">
            No form Found</p>
        </ng-container>
      </div>
      <!-- End of scrollable content area for Form tab -->
    </div>
    <!-- <div id="modal" *ngIf="!formLoaded" style="display: none;"></div> -->
  </div>
</div>
</div>
  <!-- Footer for Form section
  <ion-footer *ngIf="showSaveButton && isFormFound && segmentValue == 'form'">
    <ion-toolbar class="toolbar" style="border: 1px solid gainsboro;--min-height:30px;">
      <ion-buttons slot="end">
        <ion-button fill="solid" color="primary" (click)="saveForm()" class="save-button">
          <ion-icon slot="start" name="save-outline"></ion-icon>
          Save Form
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-footer> -->

  <!-- Footer for Permit Details section -->
  <ion-footer *ngIf="segmentValue == 'details' && !isReport && permit.STATUS != 'CLOSED' && permit.STATUS != 'CANCELLED'" mode="ios">
    <ion-toolbar mode="ios" style="--background: #ffffff; border-top: 1px solid #dedede; padding: 8px 16px;">
      <ion-buttons slot="end">
        <!-- Cancel button -->
        <ion-button color="danger" mode="md" fill="solid"
          style="--border-radius: 8px; font-weight: 600; margin-right: 8px;"
          (click)="closeModal()">
          Cancel
        </ion-button>

        <!-- Status-specific action buttons -->
        <ion-button *ngIf="permit.STATUS == 'OPEN' && permit.SYNC_STATUS == 0 && !isPermitIsExpired(permit) && !permit.isShowDetailsButton"
          color="primary" mode="md" fill="solid" [disabled]="isPermitIsExpired(permit)"
          style="--border-radius: 8px; font-weight: 600; margin-right: 8px;"
          (click)="permitAction('OPEN', permit, false);$event.stopPropagation();">
          Submit for Review
        </ion-button>

        <ion-button *ngIf="permit.STATUS == 'IN_REVIEW' && shouldShowApproveButtonInStake() && permit.SYNC_STATUS == 0 && !isPermitIsExpired(permit) && !permit.isRejected"
          color="primary" mode="md" fill="solid" [disabled]="isPermitIsExpired(permit) || permit.APPROVAL_SUMMARY == 'R'"
          style="--border-radius: 8px; font-weight: 600; margin-right: 8px;"
          (click)="permitAction('IN_REVIEW', permit, false );$event.stopPropagation();">
          Approve
        </ion-button>

        <ion-button *ngIf="permit.STATUS == 'APPROVED' && permit.SYNC_STATUS == 0 && !isPermitIsExpired(permit)"
          color="primary" mode="md" fill="solid" [disabled]="isPermitIsExpired(permit)"
          style="--border-radius: 8px; font-weight: 600; margin-right: 8px;"
          (click)="permitAction('APPROVED', permit, false);$event.stopPropagation();">
          Issue
        </ion-button>

        <ion-button *ngIf="permit.STATUS == 'ISSUED' && userRoleData[0].EXTEND == 'true' && permit.SYNC_STATUS == 0 && !isPermitIsExpired(permit)"
          color="primary" mode="md" fill="solid" [disabled]="isPermitIsExpired(permit)"
          style="--border-radius: 8px; font-weight: 600; margin-right: 8px;"
          (click)="permitAction('REVISE', permit, false);$event.stopPropagation();">
          {{ "Revise" | translate }}
        </ion-button>

        <ion-button *ngIf="permit.STATUS == 'ISSUED' && userRoleData[0].CLOSE == 'true'"
          color="primary" mode="md" fill="solid" [disabled]="isPermitIsExpired(permit) || !isApproveButtonDisabled('CLOSE')"
          style="--border-radius: 8px; font-weight: 600; margin-right: 8px;"
          (click)="permitAction('CLOSED', permit, false);$event.stopPropagation();">
          Close
        </ion-button>

        <!-- Save button -->
        <ion-button *ngIf="!isPermitIsExpired(permit) && permit.APPROVAL_SUMMARY != 'R' || isPermitIsExpired(permit) && permit.IS_EXTENDED && permit.EXTENSION_DATE == null"
          color="success" mode="md" fill="solid"
          style="--border-radius: 8px; font-weight: 600; --padding-start: 24px; --padding-end: 24px;"
          (click)="save()">
          <ion-icon slot="start" name="save-outline"></ion-icon>
          Save
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-footer>

  <!-- Footer for Report section -->
  <ion-footer *ngIf="isReport && permitForm?.FORM_GUID" mode="ios">
    <ion-toolbar mode="ios">
      <ion-buttons slot="end">
        <ion-button color="primary" mode="md" (click)="generatePDF()">
          <ion-icon slot="start" name="document-text-outline"></ion-icon>
          Generate PDF
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-footer>

<ion-content *ngIf="setBarcode" [ngClass]="{'display-middle': setBarcode}">
  <app-ngx-scanner></app-ngx-scanner>
</ion-content>