<div>
<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()"><ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>
</ion-header>
  <div class="form-container">
    <div class="form-row">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Enter Facility ID" [disabled]="mode == 1" [(ngModel)]="facility.FACILITY_ID"
          required="true" labelPlacement="stacked" fill="outline" maxlength="5">
          <div slot="label">Facility ID <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="business-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
          placeholder="Enter Facility Name" [(ngModel)]="facility.NAME" maxlength="40"
          required="true" labelPlacement="stacked" fill="outline">
          <div slot="label">Facility Name <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="business-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <ion-item lines="none" class="boundary-field">
          <ion-label position="stacked">Geo Boundary <small>(submeter accuracy)</small></ion-label>
          <div class="boundary-input-container">
            <ion-input readonly [(ngModel)]="facility.BOUNDARY" placeholder="No boundary defined"></ion-input>
            <ion-button fill="clear" (click)="openBoundaryPicker()">
              <ion-icon slot="icon-only" name="map-outline"></ion-icon>
            </ion-button>
          </div>
          <div class="boundary-note" *ngIf="facility.BOUNDARY">
            <ion-icon name="information-circle-outline"></ion-icon>
            Boundary coordinates stored with 6 decimal precision for submeter accuracy
          </div>
        </ion-item>
      </div>
    </div>

    <p class="error">{{displayError | translate}}</p>
  </div>
</div>

<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="closeModal()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!facility.NAME || !facility.FACILITY_ID" (click)="addFacility()">{{ buttonText }}</ion-button>
  </ion-toolbar>
</ion-footer>
