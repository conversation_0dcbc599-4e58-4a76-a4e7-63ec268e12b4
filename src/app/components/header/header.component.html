<div class="header-container">
  <div class="logo-container">
    <img src="assets/logo.svg" alt="Company Logo" class="logo" />
  </div>

  <div class="actions-container">
    <!-- Facility selection button with current facility name -->
    <button class="facility-select-btn" (click)="openFacilitySelector()">
      <ion-icon name="business-outline" class="facility-icon"></ion-icon>
      <span class="facility-name">{{ currentFacilityName || 'Select Facility' }}</span>
      <ion-icon name="chevron-down-outline" class="dropdown-icon"></ion-icon>
    </button>

    <!-- User actions menu -->
    // ...existing code...
  </div>
</div>