# Development Setup

## Required Versions
- Node.js: 18.13.0
- npm: 8.19.3

## Setup Options

### Option 1: Using Volta (Recommended)
```bash
# Install Volta
curl https://get.volta.sh | bash
source ~/.bash_profile

# Install required versions (already configured in package.json)
volta install node@18.13.0
volta install npm@8.19.3

# Navigate to project and pin versions
cd nustar-work-permit
volta pin node@18.13.0
volta pin npm@8.19.3
```

### Option 2: Using nvm
```bash
# Install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# Use project version
nvm use  # Uses .nvmrc file
npm install -g npm@8.19.3
```

## Installation
```bash
# Install dependencies
npm install --legacy-peer-deps

# Verify versions
node --version  # Should be v18.13.0
npm --version   # Should be 8.19.3

# Build project
npm run build
```

## Notes
- The project uses `--legacy-peer-deps` flag to resolve ZXing dependency conflicts
- This is configured in `.npmrc` file
- Volta configuration is in `package.json` under "volta" section
