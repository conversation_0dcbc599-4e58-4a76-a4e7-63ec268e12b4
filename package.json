{"name": "Permits", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular-devkit/schematics-cli": "^16.2.2", "@angular/animations": "^16.0.0", "@angular/cdk": "^16.2.14", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/google-maps": "^16.2.12", "@angular/material": "^16.2.14", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@awesome-cordova-plugins/android-permissions": "^6.6.0", "@awesome-cordova-plugins/barcode-scanner": "^6.6.0", "@awesome-cordova-plugins/calendar": "^6.14.0", "@awesome-cordova-plugins/camera": "^6.6.0", "@awesome-cordova-plugins/camera-preview": "^6.6.0", "@awesome-cordova-plugins/core": "^6.6.0", "@awesome-cordova-plugins/device": "^6.6.0", "@awesome-cordova-plugins/diagnostic": "^6.4.0", "@awesome-cordova-plugins/file": "^6.4.0", "@awesome-cordova-plugins/file-opener": "^6.4.0", "@awesome-cordova-plugins/geolocation": "^6.4.0", "@awesome-cordova-plugins/in-app-browser": "^6.6.0", "@awesome-cordova-plugins/keyboard": "^6.4.0", "@awesome-cordova-plugins/location-accuracy": "^6.6.0", "@awesome-cordova-plugins/network": "^6.6.0", "@awesome-cordova-plugins/printer": "^6.6.0", "@awesome-cordova-plugins/screen-orientation": "^6.6.0", "@awesome-cordova-plugins/social-sharing": "^6.4.0", "@awesome-cordova-plugins/splash-screen": "^6.4.0", "@awesome-cordova-plugins/status-bar": "^6.4.0", "@awesome-cordova-plugins/unvired-cordova-sdk": "^6.4.0", "@danielmoncada/angular-datetime-picker": "^16.1.0", "@fortawesome/fontawesome-free": "^6.4.2", "@ionic/angular": "^7.4.0", "@ionic/cli": "^7.1.1", "@ionic/cordova-builders": "^10.0.0", "@ionic/core": "^7.3.4", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@swimlane/ngx-charts": "^20.5.0", "@types/google.maps": "^3.58.1", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "@zxing/ngx-scanner": "^17.0.0", "browser-image-compression": "^2.0.2", "clean": "^4.0.2", "fflate": "^0.8.1", "font-awesome": "^4.7.0", "hammerjs": "^2.0.8", "ionicons": "^7.0.0", "jquery": "^3.7.1", "js-sha256": "^0.10.1", "jszip": "^3.10.1", "markerjs2": "^2.32.0", "material-design-icons-iconfont": "^6.7.0", "moment": "^2.29.4", "ngx-pagination": "^6.0.3", "ngx-toastr": "^17.0.2", "npm-check": "^6.0.1", "object-hash": "^3.0.0", "odometer": "^0.4.8", "odometer.js": "^1.0.0", "rxjs": "^7.5.0", "rxjs-compat": "^6.5.2", "swiper": "^10.3.1", "tslib": "^2.3.0", "uuid": "^9.0.1", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.0", "@angular-eslint/builder": "^16.0.0", "@angular-eslint/eslint-plugin": "^16.0.0", "@angular-eslint/eslint-plugin-template": "^16.0.0", "@angular-eslint/schematics": "^16.0.0", "@angular-eslint/template-parser": "^16.0.0", "@angular/cli": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/compiler-cli": "^16.0.0", "@angular/language-service": "^16.0.0", "@ionic/angular-toolkit": "^9.0.0", "@types/hammerjs": "^2.0.40", "@types/jasmine": "~4.3.0", "@types/jquery": "^3.5.29", "@types/node": "^12.11.1", "@types/uuid": "^9.0.5", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "cordova-android": "^12.0.1", "cordova-browser": "^7.0.0", "cordova-ios": "^7.1.0", "cordova-plugin-android-permissions": "^1.1.5", "cordova-plugin-androidx": "^3.0.0", "cordova-plugin-androidx-adapter": "^1.1.3", "cordova-plugin-calendar": "^5.1.6", "cordova-plugin-compat": "^1.2.0", "cordova-plugin-device": "^3.0.0", "cordova-plugin-file": "^8.0.0", "cordova-plugin-file-opener2": "^4.0.0", "cordova-plugin-firebase-crash": "^8.0.2", "cordova-plugin-geolocation": "^5.0.0", "cordova-plugin-hotspot": "^1.2.10", "cordova-plugin-inappbrowser": "^5.0.0", "cordova-plugin-ionic-keyboard": "^2.0.5", "cordova-plugin-ionic-webview": "^5.0.0", "cordova-plugin-network-information": "^3.0.0", "cordova-plugin-printer": "^0.8.0", "cordova-plugin-request-location-accuracy": "^2.3.0", "cordova-plugin-screen-orientation": "^3.0.3", "cordova-plugin-splashscreen": "5.0.2", "cordova-plugin-statusbar": "^2.4.2", "cordova-plugin-unvired-camera-preview": "^0.0.2", "cordova-plugin-unvired-location-sdk": "^0.0.5", "cordova-plugin-unvired-push-sdk": "^0.0.2", "cordova-plugin-unvired-sdk": "^3.0.385", "cordova-plugin-x-socialsharing": "^6.0.4", "cordova-support-android-plugin": "^2.0.4", "cordova.plugins.diagnostic": "^7.1.3", "es6-promise-plugin": "^4.2.2", "eslint": "^7.26.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "phonegap-plugin-barcodescanner": "github:shyamalaUnv/phonegap-plugin-barcodescanner", "ts-node": "^8.3.0", "typescript": "~5.0.2"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-statusbar": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-ionic-webview": {}, "cordova-plugin-ionic-keyboard": {}, "cordova-plugin-android-permissions": {}, "cordova-plugin-androidx-adapter": {}, "cordova-plugin-androidx": {}, "cordova-plugin-compat": {}, "cordova-plugin-geolocation": {"GPS_REQUIRED": "true"}, "cordova-plugin-hotspot": {}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-printer": {"ANDROID_SUPPORT_V4_VERSION": "28.+"}, "cordova-plugin-screen-orientation": {}, "cordova-plugin-x-socialsharing": {"PHOTO_LIBRARY_ADD_USAGE_DESCRIPTION": "This app requires photo library access to function properly.", "PHOTO_LIBRARY_USAGE_DESCRIPTION": "This app requires photo library access to function properly."}, "cordova-plugin-network-information": {}, "cordova-plugin-request-location-accuracy": {"PLAY_SERVICES_LOCATION_VERSION": "16.+"}, "cordova-plugin-unvired-camera-preview": {}, "cordova-plugin-unvired-push-sdk": {}, "cordova-support-android-plugin": {}, "cordova.plugins.diagnostic": {"ANDROIDX_VERSION": "1.0.0", "ANDROIDX_APPCOMPAT_VERSION": "1.3.1"}, "es6-promise-plugin": {}, "cordova-plugin-file": {"ANDROIDX_WEBKIT_VERSION": "1.4.0"}, "cordova-plugin-file-opener2": {}, "phonegap-plugin-barcodescanner": {"ANDROID_SUPPORT_V4_VERSION": "27.+"}, "cordova-plugin-unvired-location-sdk": {}, "cordova-plugin-unvired-sdk": {}, "cordova-plugin-camera": {"ANDROIDX_CORE_VERSION": "1.6.+"}, "cordova-plugin-firebase-crash": {"CRASHLYTICS_COLLECTION_ENABLED": "true", "ANDROID_FIREBASE_BOM_VERSION": "32.5.0"}, "cordova-plugin-device": {}}, "platforms": ["ios", "android", "browser"]}, "volta": {"node": "18.13.0", "npm": "8.19.3"}}